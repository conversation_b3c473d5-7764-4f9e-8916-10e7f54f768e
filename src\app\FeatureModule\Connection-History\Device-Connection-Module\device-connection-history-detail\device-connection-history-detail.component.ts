import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { BACK_BTN_TEXT, DEVICE_CONNECTION_HISTORY_DEVICE_MODEL, DEVICE_CONNECTION_HISTORY_LAST_CONNECTED_FROM, DEVICE_CONNECTION_HISTORY_MANUFACTURER, DEVICE_CONNECTION_HISTORY_OS_TYPE, DEVICE_CONNECTION_HISTORY_SERIAL_NUMBER_OR_HW_ID, DEVICE_PROBE_CONNECTION_HISTORY_APP_VERSION, DEVICE_PROBE_CONNECTION_HISTORY_CONNECTION_TYPE, DEVICE_PROBE_CONNECTION_HISTORY_LAST_CONNECTED_DATE, DEVICE_PROBE_CONNECTION_HISTORY_PACKAGE_VERSION, DEVICE_PROBE_CONNECTION_HISTORY_PIMS_DB_VERSION, DEVICE_PROBE_CONNECTION_HISTORY_PROBE_PART_NUMBER, DEVICE_PROBE_CONNECTION_HISTORY_PROBE_SERIAL_NUMBER, DEVICE_PROBE_CONNECTION_HISTORY_PROBE_TYPE, DEVICE_PROBE_CONNECTION_HISTORY_SETTING_DB_VERSION, DEVICE_PROBE_CONNECTION_HISTORY_TIMEZONE, DEVICE_PROBE_CONNECTION_HISTORY_UPS_DB_VERSION, DeviceConnectionHistoryDetailResource, FILTER_CLEAR_BUTTON, FILTER_SEARCH_BUTTON, ITEMS_PER_PAGE, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_ERROR_MESSAGE, SPECIAL_CHARACTER_PATTERN } from 'src/app/app.constants';
import { DeviceConnectionHistoryDetails } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryDetails.model';
import { DeviceProbeConnectionHistory } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistory.model';
import { DeviceProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistorySearchRequestBody.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { ConnectionTypeEnum } from 'src/app/shared/enum/ConnectionHistory/ConnectionTypeEnum.enum';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';
import { OSTypeEnum } from 'src/app/shared/enum/Probe/OSTypeEnum.enum';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { DeviceConnectionHistoryOperationService } from '../Device-Connection-History-Service/Device-Connection-History-Operation/device-connection-history-operation-service.service';

@Component({
  selector: 'app-device-connection-history-detail',
  templateUrl: './device-connection-history-detail.component.html',
  styleUrl: './device-connection-history-detail.component.css'
})
export class DeviceConnectionHistoryDetailComponent implements OnInit {

  @Input("deviceConnectionHistoryId") deviceConnectionHistoryId: number;
  @Output("showDeviceConnectionHistoryList") showDeviceConnectionHistoryList = new EventEmitter();

  backBtnText: string = BACK_BTN_TEXT;
  serialNumberOrHwId: string = DEVICE_CONNECTION_HISTORY_SERIAL_NUMBER_OR_HW_ID;
  deviceModel: string = DEVICE_CONNECTION_HISTORY_DEVICE_MODEL;
  manufacturer: string = DEVICE_CONNECTION_HISTORY_MANUFACTURER;
  osType: string = DEVICE_CONNECTION_HISTORY_OS_TYPE;
  lastConnectedDate: string = DEVICE_CONNECTION_HISTORY_LAST_CONNECTED_FROM;

  // Probe connection history table headers
  probeSerialNumber: string = DEVICE_PROBE_CONNECTION_HISTORY_PROBE_SERIAL_NUMBER;
  probeType: string = DEVICE_PROBE_CONNECTION_HISTORY_PROBE_TYPE;
  probePartNumber: string = DEVICE_PROBE_CONNECTION_HISTORY_PROBE_PART_NUMBER;
  appVersion: string = DEVICE_PROBE_CONNECTION_HISTORY_APP_VERSION;
  packageVersion: string = DEVICE_PROBE_CONNECTION_HISTORY_PACKAGE_VERSION;
  pimsDbVersion: string = DEVICE_PROBE_CONNECTION_HISTORY_PIMS_DB_VERSION;
  settingDbVersion: string = DEVICE_PROBE_CONNECTION_HISTORY_SETTING_DB_VERSION;
  upsDbVersion: string = DEVICE_PROBE_CONNECTION_HISTORY_UPS_DB_VERSION;
  timezone: string = DEVICE_PROBE_CONNECTION_HISTORY_TIMEZONE;
  probeLastConnectedDate: string = DEVICE_PROBE_CONNECTION_HISTORY_LAST_CONNECTED_DATE;
  connectionType: string = DEVICE_PROBE_CONNECTION_HISTORY_CONNECTION_TYPE;

  deviceConnectionHistoryDetailResponse: DeviceConnectionHistoryDetails = null;
  deviceProbeConnectionHistoryList: DeviceProbeConnectionHistory[] = [];

  loading: boolean = false;
  deviceConnectionHistoryDisplay: boolean = false;
  probeDetailPageDisplay: boolean = false;
  deviceDetailPageDisplay: boolean = false;
  deviceConnectionHistoryDetailResource = DeviceConnectionHistoryDetailResource;
  productEntityId: number = null;


  // Pagination for probe connection history
  itemsPerPage: number = ITEMS_PER_PAGE;
  page: number = 1;
  totalItems: number = 0;
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;
  // show entry selection
  dataSizes: string[] = [];
  probeTypeEnum = ProbeTypeEnum;
  osTypeEnum = OSTypeEnum;
  connectionTypeEnum = ConnectionTypeEnum;

  // Filter properties
  isFilterHidden: boolean = true;
  hideShowFilterButtonText: string = 'Hide Filter';

  // Filter constants
  searchBtnText: string = FILTER_SEARCH_BUTTON;
  clearBtnText: string = FILTER_CLEAR_BUTTON;
  textBoxMaxLengthMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;

  // Filter form
  filterProbeConnectionHistoryForm = new FormGroup({
    probeSerialNumber: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    probeType: new FormControl([], []),
    probePartNumber: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    connectionType: new FormControl([], [])
  });

  // Filter dropdown settings and data
  dropdownSettingsForProbeType: MultiSelectDropdownSettings = null;
  dropdownSettingsForConnectionType: MultiSelectDropdownSettings = null;
  probeTypesList: Array<any> = [];
  connectionTypesList: Array<any> = [];

  // Filter search request body for caching
  deviceProbeConnectionHistorySearchRequestBody: DeviceProbeConnectionHistorySearchRequestBody = new DeviceProbeConnectionHistorySearchRequestBody();

  constructor(
    private deviceConnectionHistoryOperationService: DeviceConnectionHistoryOperationService,
    private commonsService: CommonsService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private validationService: ValidationService
  ) { }

  public ngOnInit(): void {
    this.loading = true;
    this.deviceConnectionHistoryDisplay = true;
    this.dataSizes = this.commonsService.accessDataSizes();
    this.isFilterHidden = false;
    this.initializeFilterSettings();
    this.loadDeviceConnectionHistoryDetail();
  }

  back() {
    this.showDeviceConnectionHistoryList.emit();
  }

  /**
  * Refresh Device Detail Page Data
  *
  * <AUTHOR>
  */
  public refreshDeviceDetailPage(): void {
    this.loadDeviceConnectionHistoryDetail();
    this.loadDeviceProbeConnectionHistoryList();
  }

  /**
  * Load device connection history detail
  *
  * <AUTHOR>
  */
  public async loadDeviceConnectionHistoryDetail(): Promise<void> {
    this.loading = true;

    const result = await this.deviceConnectionHistoryOperationService.loadDeviceConnectionHistoryDetail(this.deviceConnectionHistoryId);

    if (result.success) {
      this.deviceConnectionHistoryDetailResponse = result.deviceDetail;
      this.loadDeviceProbeConnectionHistoryList();
    } else {
      this.deviceConnectionHistoryDetailResponse = null;
    }
  }

  /**
  * Load device probe connection history list
  *
  * <AUTHOR>
  */
  public async loadDeviceProbeConnectionHistoryList(): Promise<void> {
    this.loading = true;

    // Use filter search request body if available, otherwise use empty one
    const searchRequestBody = this.deviceProbeConnectionHistorySearchRequestBody || new DeviceProbeConnectionHistorySearchRequestBody(null, null, null, null);
    const pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage
    };

    const result = await this.deviceConnectionHistoryOperationService.loadDeviceProbeConnectionHistoryList(
      this.deviceConnectionHistoryId,
      searchRequestBody,
      pageObj
    );

    if (result.success) {
      this.deviceProbeConnectionHistoryList = result.deviceProbeConnectionHistoryList;
      this.totalRecordDisplay = result.totalRecordDisplay;
      this.totalRecord = result.totalRecord;
      this.totalItems = result.totalItems;
      this.page = result.page;
    } else {
      this.deviceProbeConnectionHistoryList = [];
      this.totalRecordDisplay = 0;
      this.totalRecord = 0;
      this.totalItems = 0;
    }

    this.loading = false;
  }

  /**
  * Handle pagination change
  *
  * <AUTHOR>
  * @param page
  */
  public onPageChange(page: number): void {
    this.page = page;
    this.loadDeviceProbeConnectionHistoryList();
  }

  /**
  * Navigate to probe detail page
  *
  * <AUTHOR>
  * @param probeSerialNumber
  */
  public navigateToProbeDetail(id: number): void {
    this.productEntityId = id;
    this.probeDetailPageDisplay = true;
    this.deviceConnectionHistoryDisplay = false;
  }

  public showDeviceConnectionHistoryDetailPage(): void {
    this.probeDetailPageDisplay = false;
    this.deviceConnectionHistoryDisplay = true;
    this.refreshDeviceDetailPage();
  }

  public navigateToDeviceDetail(): void {
    this.productEntityId = this.deviceConnectionHistoryDetailResponse.id;
    this.deviceDetailPageDisplay = true;
    this.deviceConnectionHistoryDisplay = false;
  }

  public showDeviceDetailPage(): void {
    this.deviceDetailPageDisplay = false;
    this.deviceConnectionHistoryDisplay = true;
    this.refreshDeviceDetailPage();
  }

  /**
  * change history items size
  * @param event 
  * <AUTHOR>
  */
  public changeDeviceHistoryDataSize(event: any): void {
    this.loading = true;
    this.itemsPerPage = event.target.value;
    this.loadDeviceProbeConnectionHistoryList();
  }

  /**
  * Refresh Device Probe Connection History
  *
  * <AUTHOR>
  */
  public refreshDeviceProbeConnectionHistory(): void {
    this.loadDeviceProbeConnectionHistoryList();
  }

  /**
  * Initialize filter settings and dropdown configurations
  * <AUTHOR>
  */
  private initializeFilterSettings(): void {
    // Initialize dropdown settings - using 'key' and 'value' to match EnumMapping structure
    this.dropdownSettingsForProbeType = new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, 'key', 'value', "Search probe type", true, false);
    this.dropdownSettingsForConnectionType = new MultiSelectDropdownSettings(false, 'Select All', 'UnSelect All', 1, true, 'key', 'value', "Search connection type", true, false);

    // Initialize dropdown data
    this.probeTypesList = this.keyValueMappingServiceService.enumOptionToList(ProbeTypeEnum);
    this.connectionTypesList = this.keyValueMappingServiceService.enumOptionToList(ConnectionTypeEnum);
  }

  /**
  * Toggle filter visibility
  * <AUTHOR>
  */
  public toggleFilter(): void {
    this.isFilterHidden = !this.isFilterHidden;
    this.hideShowFilterButtonText = this.isFilterHidden ? 'Show Filter' : 'Hide Filter';
  }

  /**
  * Search probe connection history with filter criteria
  * <AUTHOR>
  */
  public searchProbeConnectionHistory(): void {
    if (this.filterProbeConnectionHistoryForm.invalid) {
      return;
    }

    const formValue = this.filterProbeConnectionHistoryForm.value;
    this.deviceProbeConnectionHistorySearchRequestBody = new DeviceProbeConnectionHistorySearchRequestBody(
      formValue.probeSerialNumber,
      formValue.probeType?.length > 0 ? formValue.probeType[0]?.key : null,
      formValue.probePartNumber,
      formValue.connectionType?.length > 0 ? formValue.connectionType[0]?.key : null
    );

    this.page = 1; // Reset to first page when searching
    this.loadDeviceProbeConnectionHistoryList();
  }

  /**
  * Clear filter form and reload data
  * <AUTHOR>
  */
  public clearFilter(): void {
    this.filterProbeConnectionHistoryForm.reset();
    this.deviceProbeConnectionHistorySearchRequestBody = new DeviceProbeConnectionHistorySearchRequestBody();
    this.page = 1; // Reset to first page when clearing
    this.loadDeviceProbeConnectionHistoryList();
  }

  /**
  * Export device probe connection history to CSV
  * <AUTHOR>
  */
  public async exportConnectionHistory(): Promise<void> {
    try {
      this.loading = true;
      await this.deviceConnectionHistoryOperationService.exportDeviceProbeConnectionHistoryCSV(
        this.deviceConnectionHistoryId,
        this.deviceConnectionHistoryDetailResource
      );
    } finally {
      this.loading = false;
    }
  }

}
