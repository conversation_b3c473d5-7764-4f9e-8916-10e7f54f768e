import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProbeConnectionHistoryDetailComponent } from './probe-connection-history-detail.component';

describe('ProbeConnectionHistoryDetailComponent', () => {
  let component: ProbeConnectionHistoryDetailComponent;
  let fixture: ComponentFixture<ProbeConnectionHistoryDetailComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ProbeConnectionHistoryDetailComponent]
    })
      .compileComponents();

    fixture = TestBed.createComponent(ProbeConnectionHistoryDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have correct selector', () => {
    expect(component).toBeDefined();
    const compiled = fixture.nativeElement;
    expect(compiled).toBeTruthy();
  });

  it('should initialize component properties', () => {
    expect(component).toBeInstanceOf(ProbeConnectionHistoryDetailComponent);
  });

  it('should render component template', () => {
    fixture.detectChanges();
    expect(fixture.nativeElement).toBeTruthy();
  });

  it('should have component defined after creation', () => {
    expect(component).toBeDefined();
    expect(component).not.toBeNull();
  });
});
