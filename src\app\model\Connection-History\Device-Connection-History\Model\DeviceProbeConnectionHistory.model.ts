import { ConnectionTypeEnum } from "src/app/shared/enum/ConnectionHistory/ConnectionTypeEnum.enum";

export class DeviceProbeConnectionHistory {
    id: number;
    probeSerialNumber: string;
    probeType: string;
    probePartNumber: string;
    connectionType: ConnectionTypeEnum;
    appVersion: string;
    osVersion: string;
    pimsDbVersion: string;
    settingsDbVersion: string;
    timezone: string;
    upsDbVersion: string;
    lastConnectedDate: number; // Epoch milliseconds

    constructor(
        id: number,
        probeSerialNumber: string,
        probeType: string,
        probePartNumber: string,
        connectionType: ConnectionTypeEnum,
        appVersion: string,
        osVersion: string,
        pimsDbVersion: string,
        settingsDbVersion: string,
        timezone: string,
        upsDbVersion: string,
        lastConnectedDate: number
    ) {
        this.id = id;
        this.probeSerialNumber = probeSerialNumber;
        this.probeType = probeType;
        this.probePartNumber = probePartNumber;
        this.connectionType = connectionType;
        this.appVersion = appVersion;
        this.osVersion = osVersion;
        this.pimsDbVersion = pimsDbVersion;
        this.settingsDbVersion = settingsDbVersion;
        this.timezone = timezone;
        this.upsDbVersion = upsDbVersion;
        this.lastConnectedDate = lastConnectedDate;
    }
}
