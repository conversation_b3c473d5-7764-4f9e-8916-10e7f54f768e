import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { Subject, firstValueFrom } from 'rxjs';
import { COMMON_SELECT_FILTER, DeviceConnectionHistoryDetailResource, DeviceConnectionHistoryListResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { DeviceConnectionHistoryListResult } from 'src/app/model/Connection-History/Device-Connection-History/Interface/DeviceConnectionHistoryListResult.interface';
import { DeviceProbeConnectionHistoryListResult } from 'src/app/model/Connection-History/Device-Connection-History/Interface/DeviceProbeConnectionHistoryListResult.interface';
import { DeviceConnectionHistoryDetails } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryDetails.model';
import { DeviceConnectionHistoryFilterAction } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryFilterAction.model';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistorySearchRequestBody.model';
import { DeviceProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistorySearchRequestBody.model';
import { DeviceConnectionHistoryExportCSVSearchRequest } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryExportCSVSearchRequest.model';
import { DeviceProbeConnectionHistoryExportCSVSearchRequest } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistoryExportCSVSearchRequest.model';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { DeviceConnectionApiCallService } from '../Device-connection-Api-Call/device-connection-api-call.service';

/**
* Device Connection History Operation Service for communication between filter and listing components
* Includes caching functionality to avoid unnecessary API calls on filter show/hide
*
* <AUTHOR>
*/
@Injectable({
  providedIn: 'root'
})
export class DeviceConnectionHistoryOperationService {

  constructor(
    private deviceConnectionApiCallService: DeviceConnectionApiCallService,
    private commonsService: CommonsService,
    private exceptionHandlingService: ExceptionHandlingService,
    private toastrService: ToastrService,
    private permissionService: PermissionService,
    private commonOperationService: CommonOperationsService,
    private downloadService: DownloadService
  ) { }

  //Refresh device Connection History List
  private deviceConnectionHistoryListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Refresh device Connection History Detail page
  private deviceConnectionHistoryDetailRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //device Connection History list filter
  private deviceConnectionHistoryListFilterRequestParameterSubject = new Subject<DeviceConnectionHistoryFilterAction>();

  /**
  * Cache for storing last applied filter data to maintain state when hiding/showing filters
  */
  private lastAppliedDeviceConnectionHistorySearchRequest: DeviceConnectionHistorySearchRequestBody = null;

  /**
  * Device Connection History List Page Refresh After some Action
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getDeviceConnectionHistoryListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.deviceConnectionHistoryListRefreshSubject;
  }

  /**
  * Device Connection History Detail Page Refresh After some Action
  * isReloadData false means delete operation and move list page
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getDeviceConnectionHistoryDetailRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.deviceConnectionHistoryDetailRefreshSubject;
  }

  /**
  * Get Device Connection History List Filter Request Parameter Subject
  * Used by listing component to subscribe to filter changes
  * <AUTHOR>
  * @returns Subject<DeviceConnectionHistoryFilterAction>
  */
  public getDeviceConnectionHistoryListFilterRequestParameterSubject(): Subject<DeviceConnectionHistoryFilterAction> {
    return this.deviceConnectionHistoryListFilterRequestParameterSubject;
  }

  /**
  * Call Device Connection History List Filter Request Parameter Subject
  * Used by filter component to emit filter changes
  * <AUTHOR>
  * @param deviceConnectionHistoryFilterAction - The filter action containing search parameters
  */
  public callDeviceConnectionHistoryListFilterRequestParameterSubject(deviceConnectionHistoryFilterAction: DeviceConnectionHistoryFilterAction): void {
    this.deviceConnectionHistoryListFilterRequestParameterSubject.next(deviceConnectionHistoryFilterAction);
  }

  /**
  * This function call the subject for reload the page data
  * Note : (DeviceConnectionHistoryListResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter
  * @param resourceName
  * @param isFilterHidden
  */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean): void {
    if (resourceName == DeviceConnectionHistoryListResource) {
      if (isFilterHidden) {
        let deviceConnectionHistorySearchRequestBody = new DeviceConnectionHistorySearchRequestBody(null, null, null, null, null);

        // Use cached filter data if available and not clearing filters
        if (this.lastAppliedDeviceConnectionHistorySearchRequest && !listingPageReloadSubjectParameter.isClearFilter) {
          deviceConnectionHistorySearchRequestBody = this.lastAppliedDeviceConnectionHistorySearchRequest;
        }

        let deviceConnectionHistoryFilterAction = new DeviceConnectionHistoryFilterAction(listingPageReloadSubjectParameter, deviceConnectionHistorySearchRequestBody);
        this.callDeviceConnectionHistoryListFilterRequestParameterSubject(deviceConnectionHistoryFilterAction);
      } else {
        // When filter is visible, call refresh subject to trigger filter component
        this.deviceConnectionHistoryListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    } else if (resourceName == DeviceConnectionHistoryDetailResource) {
      this.deviceConnectionHistoryDetailRefreshSubject.next(listingPageReloadSubjectParameter);
    }
  }

  /**
   * Call filter page subject for reload page
   * Used to maintain filter state when navigating between listing and detail pages
   * <AUTHOR>
   * @param isDefaultPageNumber - Whether to reset to default page number
   * @param isClearFilter - Whether to clear filters
   */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    this.callRefreshPageSubject(listingPageReloadSubjectParameter, DeviceConnectionHistoryListResource, false);
  }

  /**
  * Load device connection history list with search parameters and pagination
  * Handles API call, response processing, and error handling
  * <AUTHOR>
  * @param deviceConnectionHistorySearchRequestBody - Search criteria for filtering
  * @param pageObj - Pagination parameters (page, size)
  * @returns Promise with device connection history list result
  */
  public async loadDeviceConnectionHistoryList(deviceConnectionHistorySearchRequestBody: DeviceConnectionHistorySearchRequestBody, pageObj: any): Promise<DeviceConnectionHistoryListResult> {
    try {
      if (this.permissionService.getConnectionHistoryPermission(PermissionAction.GET_CONNECTION_HISTORY_ACTION)) {
        const response = await firstValueFrom(this.deviceConnectionApiCallService.getDeviceConnectionHistoryList(deviceConnectionHistorySearchRequestBody, pageObj));

        if (response.status === 200 && response.body) {
          const deviceConnectionHistoryData = response.body;
          return {
            success: true,
            deviceConnectionHistoryList: deviceConnectionHistoryData.content,
            totalRecordDisplay: deviceConnectionHistoryData.numberOfElements,
            totalRecord: deviceConnectionHistoryData.totalElements,
            localDeviceConnectionHistoryList: deviceConnectionHistoryData.content,
            totalItems: deviceConnectionHistoryData.totalElements,
            page: deviceConnectionHistoryData.number + 1
          };
        } else {
          return this.getEmptyDeviceConnectionHistoryListResult();
        }
      } else {
        this.toastrService.error('Insufficient permissions to load device connection history list');
        return this.getEmptyDeviceConnectionHistoryListResult();
      }
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return this.getEmptyDeviceConnectionHistoryListResult();
    }
  }

  /**
  * Get empty device connection history list result for error cases
  * <AUTHOR>
  * @returns DeviceConnectionHistoryListResult
  */
  private getEmptyDeviceConnectionHistoryListResult(): DeviceConnectionHistoryListResult {
    return {
      success: false,
      deviceConnectionHistoryList: [],
      totalRecordDisplay: 0,
      totalRecord: 0,
      localDeviceConnectionHistoryList: [],
      totalItems: 0,
      page: 0
    };
  }

  /**
  * Process filter search and validate form
  * <AUTHOR>
  * @param formValue - Form values from filter component
  * @param isFormInvalid - Whether form is invalid
  * @param defaultListingPageReloadSubjectParameter - Default reload parameters
  * @returns boolean indicating if search should proceed
  */
  public processFilterSearch(formValue: any, isFormInvalid: boolean, defaultListingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): boolean {
    if (isFormInvalid) {
      return false;
    }

    if (!this.validateDeviceConnectionHistoryFilterForm(formValue)) {
      return false;
    }

    const deviceConnectionHistorySearchRequestBody = this.buildDeviceConnectionHistoryFilterRequestBody(formValue);
    const deviceConnectionHistoryFilterAction = new DeviceConnectionHistoryFilterAction(defaultListingPageReloadSubjectParameter, deviceConnectionHistorySearchRequestBody);
    this.callDeviceConnectionHistoryListFilterRequestParameterSubject(deviceConnectionHistoryFilterAction);
    return true;
  }

  /**
  * Validate device connection history filter form
  * <AUTHOR>
  * @param formValue - Form values to validate
  * @returns boolean indicating if validation passed
  */
  public validateDeviceConnectionHistoryFilterForm(formValue: any): boolean {
    const { serialNumber, deviceModel, manufacturer, osType, fromLastConnectedDate, toLastConnectedDate } = formValue;

    // 1. At least one filter selected
    const hasAnyFilter = serialNumber || deviceModel || manufacturer || (osType?.length ?? 0) > 0 || fromLastConnectedDate || toLastConnectedDate;

    if (!hasAnyFilter) {
      this.toastrService.info(COMMON_SELECT_FILTER);
      return false;
    }
    if (!this.commonOperationService.dateValidation(fromLastConnectedDate, toLastConnectedDate)) {
      return false;
    }

    return true;
  }


  /**
  * Build device connection history filter request body from form values
  * <AUTHOR>
  * @param formValue - Form values from filter component
  * @returns DeviceConnectionHistorySearchRequestBody
  */
  public buildDeviceConnectionHistoryFilterRequestBody(formValue: any): DeviceConnectionHistorySearchRequestBody {
    const manufacturer = this.commonsService.checkNullFieldValue(formValue.manufacturer);
    const deviceModel = this.commonsService.checkNullFieldValue(formValue.deviceModel);
    const serialNumber = this.commonsService.checkNullFieldValue(formValue.serialNumber);
    const osType = this.commonsService.getSelectedValueFromEnum(formValue.osType);
    const lastConntectedFrom = this.commonsService.checkValueIsNullOrEmpty(formValue.fromLastConnectedDate) ? null : new Date(formValue.fromLastConnectedDate).getTime();
    const lastConntectedTo = this.commonsService.checkValueIsNullOrEmpty(formValue.toLastConnectedDate) ? null : this.commonsService.getEndTimeOfDay(new Date(formValue.toLastConnectedDate)).getTime();

    return new DeviceConnectionHistorySearchRequestBody(
      manufacturer,
      deviceModel,
      serialNumber,
      osType ? osType[0] : null,
      lastConntectedFrom,
      lastConntectedTo
    );
  }

  /**
  * Clear all filters and refresh the listing
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter - Reload parameters
  */
  public clearAllFiltersAndRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    const emptyFilterRequestBody = new DeviceConnectionHistorySearchRequestBody(null, null, null, null, null);
    const deviceConnectionHistoryFilterAction = new DeviceConnectionHistoryFilterAction(listingPageReloadSubjectParameter, emptyFilterRequestBody);
    this.callDeviceConnectionHistoryListFilterRequestParameterSubject(deviceConnectionHistoryFilterAction);
  }

  /**
   * Set last applied device connection history search request for caching
   * <AUTHOR>
   * @param deviceConnectionHistorySearchRequestBody - Filter data to cache
   */
  public setLastAppliedDeviceConnectionHistorySearchRequest(deviceConnectionHistorySearchRequestBody: DeviceConnectionHistorySearchRequestBody): void {
    this.lastAppliedDeviceConnectionHistorySearchRequest = deviceConnectionHistorySearchRequestBody;
  }

  /**
   * Get last applied device connection history search request from cache
   * <AUTHOR>
   * @returns DeviceConnectionHistorySearchRequestBody - Cached filter data
   */
  public getLastAppliedDeviceConnectionHistorySearchRequest(): DeviceConnectionHistorySearchRequestBody {
    return this.lastAppliedDeviceConnectionHistorySearchRequest;
  }

  /**
  * Load device connection history detail
  * <AUTHOR>
  * @param deviceId - Device ID to get details for
  * @returns Promise with device connection history details
  */
  public async loadDeviceConnectionHistoryDetail(deviceId: number): Promise<{ success: boolean; deviceDetail?: DeviceConnectionHistoryDetails }> {
    try {
      if (this.permissionService.getConnectionHistoryPermission(PermissionAction.GET_CONNECTION_HISTORY_ACTION)) {
        const response = await firstValueFrom(this.deviceConnectionApiCallService.getDeviceConnectionHistoryDetail(deviceId));

        if (response.status === 200 && response.body) {
          return {
            success: true,
            deviceDetail: response.body
          };
        } else {
          return { success: false };
        }
      } else {
        this.toastrService.error('Insufficient permissions to load device connection history detail');
        return { success: false };
      }
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return { success: false };
    }
  }

  /**
  * Load device probe connection history list with search parameters and pagination
  * <AUTHOR>
  * @param deviceId - Device ID to get probe connections for
  * @param deviceProbeConnectionHistorySearchRequestBody - Search criteria for filtering
  * @param pageObj - Pagination parameters (page, size)
  * @returns Promise with device probe connection history list result
  */
  public async loadDeviceProbeConnectionHistoryList(deviceId: number, deviceProbeConnectionHistorySearchRequestBody: DeviceProbeConnectionHistorySearchRequestBody, pageObj: any): Promise<DeviceProbeConnectionHistoryListResult> {
    try {
      if (this.permissionService.getConnectionHistoryPermission(PermissionAction.GET_CONNECTION_HISTORY_ACTION)) {
        const response = await firstValueFrom(this.deviceConnectionApiCallService.getDeviceProbeConnectionHistoryList(deviceId, deviceProbeConnectionHistorySearchRequestBody, pageObj));

        if (response.status === 200 && response.body) {
          const deviceProbeConnectionHistoryData = response.body;
          return {
            success: true,
            deviceProbeConnectionHistoryList: deviceProbeConnectionHistoryData.content,
            totalRecordDisplay: deviceProbeConnectionHistoryData.numberOfElements,
            totalRecord: deviceProbeConnectionHistoryData.totalElements,
            localDeviceProbeConnectionHistoryList: deviceProbeConnectionHistoryData.content,
            totalItems: deviceProbeConnectionHistoryData.totalElements,
            page: deviceProbeConnectionHistoryData.number + 1
          };
        } else {
          return this.getEmptyDeviceProbeConnectionHistoryListResult();
        }
      } else {
        this.toastrService.error('Insufficient permissions to load device probe connection history list');
        return this.getEmptyDeviceProbeConnectionHistoryListResult();
      }
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return this.getEmptyDeviceProbeConnectionHistoryListResult();
    }
  }

  /**
  * Get empty device probe connection history list result for error cases
  * <AUTHOR>
  * @returns DeviceProbeConnectionHistoryListResult
  */
  private getEmptyDeviceProbeConnectionHistoryListResult(): DeviceProbeConnectionHistoryListResult {
    return {
      success: false,
      deviceProbeConnectionHistoryList: [],
      totalRecordDisplay: 0,
      totalRecord: 0,
      localDeviceProbeConnectionHistoryList: [],
      totalItems: 0,
      page: 0
    };
  }

  /**
  * Export device connection history data to CSV format
  * Handles both generation and download of CSV file
  * <AUTHOR>
  * @param deviceConnectionHistoryIds - Selected device connection history IDs for export
  * @param resourceName - Resource name for loading state management
  * @returns Promise indicating export completion
  */
  public async exportDeviceConnectionHistoryCSV(deviceConnectionHistoryIds: number[], resourceName: string): Promise<void> {
    try {
      this.commonsService.setLoading(true, resourceName);

      // Create empty search request for export - filter will be handled by filter component
      const emptySearchRequest = new DeviceConnectionHistorySearchRequestBody();
      const deviceConnectionHistoryExportCSVSearchRequest = new DeviceConnectionHistoryExportCSVSearchRequest(
        deviceConnectionHistoryIds,
        new Date().getTimezoneOffset(),
        emptySearchRequest
      );

      // Generate CSV file
      const generateResponse = await firstValueFrom(
        this.deviceConnectionApiCallService.generateCSVFileForDeviceConnectionHistory(deviceConnectionHistoryExportCSVSearchRequest)
      );

      const fileName = generateResponse.body.fileName;

      // Download the generated file
      const downloadResponse = await firstValueFrom(
        this.deviceConnectionApiCallService.downloadCSVFileForDeviceConnectionHistory(fileName)
      );

      this.downloadService.downloadExportCSV("List_of_Device_Connection_History.xls", downloadResponse);
      this.toastrService.success('CSV exported successfully');

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
    } finally {
      this.commonsService.setLoading(false, resourceName);
    }
  }

  /**
  * Export device probe connection history data to CSV format
  * Handles both generation and download of CSV file
  * <AUTHOR>
  * @param deviceConnectionHistoryId - Device connection history ID
  * @param resourceName - Resource name for loading state management
  * @returns Promise indicating export completion
  */
  public async exportDeviceProbeConnectionHistoryCSV(deviceConnectionHistoryId: number, resourceName: string): Promise<void> {
    try {
      this.commonsService.setLoading(true, resourceName);

      // Create empty search request for export - filter will be handled by filter component
      const emptySearchRequest = new DeviceProbeConnectionHistorySearchRequestBody();
      const deviceProbeConnectionHistoryExportCSVSearchRequest = new DeviceProbeConnectionHistoryExportCSVSearchRequest(
        deviceConnectionHistoryId,
        new Date().getTimezoneOffset(),
        emptySearchRequest
      );

      // Generate CSV file
      const generateResponse = await firstValueFrom(
        this.deviceConnectionApiCallService.generateCSVFileForDeviceProbeConnectionHistory(deviceProbeConnectionHistoryExportCSVSearchRequest)
      );

      const fileName = generateResponse.body.fileName;

      // Download the generated file
      const downloadResponse = await firstValueFrom(
        this.deviceConnectionApiCallService.downloadCSVFileForDeviceProbeConnectionHistory(fileName)
      );

      this.downloadService.downloadExportCSV("List_of_Device_Probe_Connection_History.xls", downloadResponse);
      this.toastrService.success('CSV exported successfully');

    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
    } finally {
      this.commonsService.setLoading(false, resourceName);
    }
  }

}
