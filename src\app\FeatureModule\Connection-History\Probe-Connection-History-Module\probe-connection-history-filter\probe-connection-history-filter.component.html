<!--####################################################-->
<!------------------Filter Start-------------------------->
<!--####################################################-->
<form id="probeConnectionHistoryFilterform" class="form" [formGroup]="filterProbeConnectionHistoryForm">

    <!-----------Serial Number start-------------->
    <div class="form-group">
        <label class="form-control-label" for="field_Serial_Number"><strong>{{serialNumberOrHwId}}</strong></label>
        <input class="form-control" type="text" formControlName="serialNumber" />
        <div *ngIf="(filterProbeConnectionHistoryForm.get('serialNumber').touched || filterProbeConnectionHistoryForm.get('serialNumber')?.dirty) && 
      filterProbeConnectionHistoryForm.get('serialNumber').invalid">
            <div *ngIf="filterProbeConnectionHistoryForm.get('serialNumber').errors['maxlength']" class="pb-2">
                <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterProbeConnectionHistoryForm.get('serialNumber').errors['pattern']" class="pb-2">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
    </div>
    <!-----------Serial Number end-------------->

    <!-----------Part Number start-------------->
    <div class="form-group">
        <label class="form-control-label" for="field_Part_number"><strong>{{partNumber}}</strong></label>
        <input class="form-control" type="text" formControlName="partNumber" />
        <div *ngIf="(filterProbeConnectionHistoryForm.get('partNumber').touched || filterProbeConnectionHistoryForm.get('partNumber')?.dirty) && 
        filterProbeConnectionHistoryForm.get('partNumber').invalid">
            <div *ngIf="filterProbeConnectionHistoryForm.get('partNumber').errors['maxlength']" class="pb-2">
                <span class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
            </div>
            <div *ngIf="filterProbeConnectionHistoryForm.get('partNumber').errors['pattern']" class="pb-2">
                <span class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
            </div>
        </div>
    </div>
    <!-----------Part Number end-------------->

    <!-------------------------------------------->
    <!-- Probe Type form group start -->
    <div class="form-group">
        <label class="form-control-label" for="field_probeType"
            id="label_probeType"><strong>{{probeType}}</strong></label>
        <!-- Probe Type selection start -->
        <ng-multiselect-dropdown id="field_osType" name="probeType" [placeholder]="''" formControlName="probeType"
            class="probePageProbeType" [settings]="dropdownSettingsForProbeType" [data]="probeTypesList">
        </ng-multiselect-dropdown>
        <!-- Probe Type selection end -->
    </div>
    <!-- Probe Type form group end -->
    <!-------------------------------------------->

    <!--------------------------->
    <!-- Last Connected From - start -->
    <!--------------------------->
    <div class="form-group">
        <label class="form-control-label"
            for="field_fromLastConnectedDate"><strong>{{lastConnectedFrom}}</strong></label>
        <mat-form-field>
            <input class="form-control" style="width:90%;display: inherit;" matInput [matDatepicker]="start"
                placeholder="Choose a Modified End Date" formControlName="fromLastConnectedDate" [max]="maxdate">
            <mat-datepicker-toggle matSuffix [for]="start"></mat-datepicker-toggle>
            <mat-datepicker #start></mat-datepicker>
        </mat-form-field>
    </div>
    <!------------------------------>
    <!-- Last Connected From -end -->
    <!------------------------------>
    <!--##################################################-->


    <!------------------------------->
    <!-- Last Connected To - start -->
    <!------------------------------->
    <div class="form-group">
        <label class="form-control-label" for="field_toLastConnectedDate"><strong>{{lastConnectedTo}}</strong></label>
        <mat-form-field>
            <input class="form-control" style="width:90%;display: inherit;" matInput [matDatepicker]="end"
                placeholder="Choose a Modified End Date" formControlName="toLastConnectedDate" [max]="maxdate">
            <mat-datepicker-toggle matSuffix [for]="end"></mat-datepicker-toggle>
            <mat-datepicker #end></mat-datepicker>
        </mat-form-field>
    </div>
    <!------------------------------>
    <!-- Last Connected To -end -->
    <!------------------------------>
    <!--##################################################-->



    <hr class="mt-1 mb-2">
    <!--####################################################-->
    <!---------Action Button Start------->
    <!--####################################################-->
    <div class="">
        <button class="btn btn-sm btn-orange mr-3" (click)="searchData()"
            [disabled]="filterProbeConnectionHistoryForm.invalid">{{searchBtnText}}</button>
        <button class="btn btn-sm btn-orange"
            (click)="clearFilter(defaultListingPageReloadSubjectParameter)">{{clearBtnText}}</button>
    </div>
    <!--####################################################-->
    <!---------Action Button End------->
    <!--####################################################-->
</form>
<!--####################################################-->
<!--Filter End-->
<!--####################################################-->