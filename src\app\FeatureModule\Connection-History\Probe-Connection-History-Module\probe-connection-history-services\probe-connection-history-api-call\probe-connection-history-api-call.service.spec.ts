import { TestBed } from '@angular/core/testing';
import { HttpTestingController } from '@angular/common/http/testing';
import { ProbeConnectionHistoryApiCallService } from './probe-connection-history-api-call.service';
import { ConfigInjectService } from 'src/app/shared/InjectService/config-inject.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { ProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistorySearchRequestBody.model';
import { ProbeConnectionHistoryPegableResponse } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistoryPegableResponse.model';
import { throwError } from 'rxjs';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';


describe('ProbeConnectionHistoryApiCallService', () => {
  let service: ProbeConnectionHistoryApiCallService;
  let httpMock: HttpTestingController;
  let mockConfigInjectService: jasmine.SpyObj<ConfigInjectService>;
  let mockCommonsService: jasmine.SpyObj<CommonsService>;

  beforeEach(() => {
    const configInjectServiceSpy = jasmine.createSpyObj('ConfigInjectService', ['getServerApiUrl']);
    const commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['handleError']);

    TestBed.configureTestingModule({
      providers: [
        ProbeConnectionHistoryApiCallService,
        { provide: ConfigInjectService, useValue: configInjectServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        commonsProviders(null)
      ]
    });

    httpMock = TestBed.inject(HttpTestingController);
    mockConfigInjectService = TestBed.inject(ConfigInjectService) as jasmine.SpyObj<ConfigInjectService>;
    mockCommonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;

    // Setup default mocks
    mockConfigInjectService.getServerApiUrl.and.returnValue('http://localhost:8080/');
    mockCommonsService.handleError.and.returnValue(throwError(() => new Error('Test error')));

    // Initialize service after mocks are set up
    service = TestBed.inject(ProbeConnectionHistoryApiCallService);
  });

  afterEach(() => {
    httpMock.verify();
  });

  // Test 1: Service creation and URL initialization (covers constructor and property initialization)
  it('should be created and initialize URL correctly', () => {
    expect(service).toBeTruthy();
    expect(service.probeConnectionHistory).toBe('http://localhost:8080/api/connection-history');
    expect(mockConfigInjectService.getServerApiUrl).toHaveBeenCalled();
  });

  // Test 2: Successful API call (covers main method path and success scenario)
  it('should get probe connection history list successfully', () => {
    const mockRequestBody = new ProbeConnectionHistorySearchRequestBody('part', ProbeTypeEnum.TORSO1, 'serial', 123, 456);
    const mockPageRequest = { page: 0, size: 10 };
    const mockResponse: ProbeConnectionHistoryPegableResponse = {
      content: [],
      totalElements: 0,
      totalPages: 0,
      size: 10,
      number: 0,
      numberOfElements: 0,
      first: true,
      last: true,
      empty: true,
      pageable: { sort: { sorted: false, unsorted: true, empty: true }, pageNumber: 0, pageSize: 10, offset: 0, paged: true, unpaged: false },
      sort: { sorted: false, unsorted: true, empty: true }
    };

    service.getProbeConnectionHistoryList(mockRequestBody, mockPageRequest).subscribe(response => {
      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne('http://localhost:8080/api/connection-history/probes/search?page=0&size=10');
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(mockRequestBody);
    req.flush(mockResponse);
  });

  // Test 3: Error handling (covers catchError pipe and error scenario)
  it('should handle HTTP error and call error handler', () => {
    const mockRequestBody = new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null);
    const mockPageRequest = { page: 0, size: 10 };

    service.getProbeConnectionHistoryList(mockRequestBody, mockPageRequest).subscribe({
      next: () => fail('Should have failed'),
      error: (error) => expect(error.message).toBe('Test error')
    });

    const req = httpMock.expectOne('http://localhost:8080/api/connection-history/probes/search?page=0&size=10');
    req.flush('Error', { status: 500, statusText: 'Internal Server Error' });
    expect(mockCommonsService.handleError).toHaveBeenCalled();
  });

  // Test 4: Empty request parameters (covers createRequestOption with empty object)
  it('should handle empty request parameters', () => {
    const mockRequestBody = new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null);
    const mockResponse: ProbeConnectionHistoryPegableResponse = {
      content: [], totalElements: 0, totalPages: 0, size: 10, number: 0, numberOfElements: 0,
      first: true, last: true, empty: true,
      pageable: { sort: { sorted: false, unsorted: true, empty: true }, pageNumber: 0, pageSize: 10, offset: 0, paged: true, unpaged: false },
      sort: { sorted: false, unsorted: true, empty: true }
    };

    service.getProbeConnectionHistoryList(mockRequestBody, {}).subscribe(response => {
      expect(response.status).toBe(200);
    });

    const req = httpMock.expectOne('http://localhost:8080/api/connection-history/probes/search');
    req.flush(mockResponse);
  });
});
