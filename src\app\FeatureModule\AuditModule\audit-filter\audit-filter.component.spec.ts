import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { MODIFIED_END_DATE_FILTER, MODIFIED_START_DATE_FILTER, AUDIT_MODULE_UNIQUE_ID_FILTER } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { AuditApiCallService } from 'src/app/shared/Service/Audit/audit-api-call.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { AuditFilterComponent } from './audit-filter.component';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';

describe('AuditFilterComponent', () => {
  let component: AuditFilterComponent;
  let fixture: ComponentFixture<AuditFilterComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const confirmDialogServiceMock = jasmine.createSpyObj('ConfirmDialogService', ['open', 'close']);
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    const spy = jasmine.createSpyObj('AuditApiCallService', ['getAuditModuleList']);
    localStorageServiceMock.retrieve.and.returnValue('mockedPermissionObject');

    await TestBed.configureTestingModule({
      declarations: [AuditFilterComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(),
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatDatepickerModule,
        MatNativeDateModule,
        BrowserAnimationsModule],
      providers: [
        CommonsService,
        CommonOperationsService,
        RoleApiCallService,
        SessionStorageService,
        HidePermissionNamePipe,
        EnumMappingDisplayNamePipe,
        KeyValueMappingServiceService,
        PrintListPipe,
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: ConfirmDialogService, useValue: confirmDialogServiceMock },
        { provide: AuditApiCallService, useValue: spy },
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AuditFilterComponent);
    component = fixture.componentInstance;

    // Detect changes after initializing the component
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it("without data search from filter", async () => {
    component.ngOnInit();
    // Wait for asynchronous tasks to complete
    await fixture.whenStable();
    fixture.detectChanges();

    // Simulate a click on the select element (make sure the ID matches)
    const selectElement = fixture.nativeElement.querySelector('#auditFilterSearch');
    selectElement.click();

    // Assert: Confirm toastrService.info was called with a "Please Select Filter To Search" message
    expect(toastrServiceMock.info).toHaveBeenCalledWith("Please Select Filter To Search");
  });

  it("only add start date filter", () => {
    component.filterAuditForm.get('modifiedStartDate').setValue(new Date('2024-10-31T18:30:00.000Z')); // Date or timestamp

    // Simulate a click on the select element (make sure the ID matches)
    const selectElement = fixture.nativeElement.querySelector('#auditFilterSearch');
    selectElement.click();

    fixture.detectChanges();

    // Assert: Confirm toastrService.info was called with an AUDIT_MODIFIED_END_DATE_FILTER message
    expect(toastrServiceMock.info).toHaveBeenCalledWith(MODIFIED_END_DATE_FILTER);
  });

  it("only add end date filter", () => {
    component.filterAuditForm.get('modifiedEndDate').setValue(new Date('2024-11-06T18:30:00.000Z')); // Date or timestamp

    // Simulate a click on the select element (make sure the ID matches)
    const selectElement = fixture.nativeElement.querySelector('#auditFilterSearch');
    selectElement.click();

    fixture.detectChanges();

    // Assert: Confirm toastrService.info was called with an AUDIT_MODIFIED_START_DATE_FILTER message
    expect(toastrServiceMock.info).toHaveBeenCalledWith(MODIFIED_START_DATE_FILTER);
  });

  it("only add uniqueId value filter", async () => {
    // Set value for 'uniqueId' in the form control
    component.filterAuditForm.get('uniqueId').setValue('akshay.dobariya');

    // Simulate a click on the select element (make sure the ID matches)
    const selectElement = fixture.nativeElement.querySelector('#auditFilterSearch');
    selectElement.click();

    fixture.detectChanges();

    // Assert: Confirm toastrService.info was called with the correct message
    expect(toastrServiceMock.info).toHaveBeenCalledWith(AUDIT_MODULE_UNIQUE_ID_FILTER);
  });

});
