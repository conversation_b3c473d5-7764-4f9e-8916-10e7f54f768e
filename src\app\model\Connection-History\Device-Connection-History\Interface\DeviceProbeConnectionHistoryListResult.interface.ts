import { DeviceProbeConnectionHistory } from "../Model/DeviceProbeConnectionHistory.model";

/**
* Device Probe Connection History List Result interface for consistent response handling
*/
export interface DeviceProbeConnectionHistoryListResult {
    success: boolean;
    deviceProbeConnectionHistoryList: DeviceProbeConnectionHistory[];
    totalRecordDisplay: number;
    totalRecord: number;
    localDeviceProbeConnectionHistoryList: DeviceProbeConnectionHistory[];
    totalItems: number;
    page: number;
}
