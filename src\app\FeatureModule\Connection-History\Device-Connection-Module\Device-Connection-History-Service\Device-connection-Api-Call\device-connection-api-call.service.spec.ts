import { HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { provideHttpClient, HttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { of, throwError } from 'rxjs';
import { DeviceConnectionApiCallService } from './device-connection-api-call.service';
import { ConfigInjectService } from 'src/app/shared/InjectService/config-inject.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistorySearchRequestBody.model';
import { DeviceConnectionHistoryExportCSVSearchRequest } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryExportCSVSearchRequest.model';
import { DeviceProbeConnectionHistoryExportCSVSearchRequest } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistoryExportCSVSearchRequest.model';
import { DeviceProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistorySearchRequestBody.model';

describe('DeviceConnectionApiCallService', () => {
  let service: DeviceConnectionApiCallService;
  let httpMock: HttpTestingController;
  let configServiceSpy: jasmine.SpyObj<ConfigInjectService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;

  const mockServerUrl = 'https://localhost:8080/';
  const mockSearchRequestBody = new DeviceConnectionHistorySearchRequestBody(
    'TestManufacturer',
    'TestModel',
    'TEST123',
    null,
    1672531200000
  );

  beforeEach(() => {
    configServiceSpy = jasmine.createSpyObj('ConfigInjectService', ['getServerApiUrl']);
    configServiceSpy.getServerApiUrl.and.returnValue(mockServerUrl);

    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['handleError']);
    commonsServiceSpy.handleError.and.callFake(() => throwError(() => new Error('Handled error')));

    TestBed.configureTestingModule({
      providers: [
        provideHttpClient(),
        provideHttpClientTesting(),
        DeviceConnectionApiCallService,
        { provide: ConfigInjectService, useValue: configServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: ExceptionHandlingService, useValue: {} }
      ]
    });

    service = TestBed.inject(DeviceConnectionApiCallService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should make a successful POST request', () => {
    service.getDeviceConnectionHistoryList(mockSearchRequestBody, { page: 0, size: 10 })
      .subscribe(response => {
        expect(response.status).toBe(200);
      });

    const req = httpMock.expectOne(request =>
      request.url === service.deviceConnectionHistory + '/devices/search' &&
      request.method === 'POST'
    );

    expect(req.request.params.get('page')).toBe('0');
    expect(req.request.params.get('size')).toBe('10');
    expect(req.request.body).toEqual(mockSearchRequestBody);

    req.flush({}, { status: 200, statusText: 'OK' });
  });

  it('should trigger commonsService.handleError on HTTP error', () => {
    service.getDeviceConnectionHistoryList(mockSearchRequestBody, {})
      .subscribe({
        next: () => fail('Expected error'),
        error: (err) => {
          expect(err.message).toBe('Handled error');
          expect(commonsServiceSpy.handleError).toHaveBeenCalled();
        }
      });

    const req = httpMock.expectOne(service.deviceConnectionHistory + '/devices/search');
    req.flush('Error', { status: 500, statusText: 'Server Error' });
  });
});
