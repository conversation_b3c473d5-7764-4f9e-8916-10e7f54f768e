import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProbeConnectionHistoryFilterComponent } from './probe-connection-history-filter.component';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { DatePipe } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MatNativeDateModule } from '@angular/material/core';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ProbeConnectionHistoryOperationService } from '../probe-connection-history-services/probe-connection-history-operation/probe-connection-history-operation.service';
import { ProbeOperationService } from 'src/app/FeatureModule/Probe/ProbeService/Probe-Operation/probe-operation.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { Subject } from 'rxjs';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistorySearchRequestBody.model';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';

describe('ProbeConnectionHistoryFilterComponent', () => {
  let component: ProbeConnectionHistoryFilterComponent;
  let fixture: ComponentFixture<ProbeConnectionHistoryFilterComponent>;
  let mockProbeConnectionHistoryService: jasmine.SpyObj<ProbeConnectionHistoryOperationService>;
  let mockProbeOperationService: jasmine.SpyObj<ProbeOperationService>;
  let mockMultiSelectDropDownSettingService: jasmine.SpyObj<MultiSelectDropDownSettingService>;
  let mockValidationService: jasmine.SpyObj<ValidationService>;

  beforeEach(async () => {
    const probeConnectionHistoryServiceSpy = jasmine.createSpyObj('ProbeConnectionHistoryOperationService', [
      'getProbeConnectionHistoryListRefreshSubject',
      'processFilterSearch',
      'buildProbeConnectionHistoryFilterRequestBody',
      'setLastAppliedProbeConnectionHistorySearchRequest',
      'getLastAppliedProbeConnectionHistorySearchRequest',
      'clearAllFiltersAndRefresh',
      'callProbeConnectionHistoryListFilterRequestParameterSubject'
    ]);

    const probeOperationServiceSpy = jasmine.createSpyObj('ProbeOperationService', [
      'getProbeTypesListFromCache',
      'getProbeTypesList'
    ]);

    const multiSelectDropDownSettingServiceSpy = jasmine.createSpyObj('MultiSelectDropDownSettingService', [
      'getProbeTypeDropdownSetting'
    ]);

    const validationServiceSpy = jasmine.createSpyObj('ValidationService', ['removeSpaces']);

    await TestBed.configureTestingModule({
      declarations: [ProbeConnectionHistoryFilterComponent],
      imports: [MatFormFieldModule, MatDatepickerModule, BrowserAnimationsModule, MatNativeDateModule, MatInputModule, ReactiveFormsModule, FormsModule, NgMultiSelectDropDownModule.forRoot()],
      providers: [
        LocalStorageService,
        SessionStorageService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        PrintListPipe,
        DatePipe,
        { provide: ProbeConnectionHistoryOperationService, useValue: probeConnectionHistoryServiceSpy },
        { provide: ProbeOperationService, useValue: probeOperationServiceSpy },
        { provide: MultiSelectDropDownSettingService, useValue: multiSelectDropDownSettingServiceSpy },
        { provide: ValidationService, useValue: validationServiceSpy },
        commonsProviders(null)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(ProbeConnectionHistoryFilterComponent);
    component = fixture.componentInstance;

    mockProbeConnectionHistoryService = TestBed.inject(ProbeConnectionHistoryOperationService) as jasmine.SpyObj<ProbeConnectionHistoryOperationService>;
    mockProbeOperationService = TestBed.inject(ProbeOperationService) as jasmine.SpyObj<ProbeOperationService>;
    mockMultiSelectDropDownSettingService = TestBed.inject(MultiSelectDropDownSettingService) as jasmine.SpyObj<MultiSelectDropDownSettingService>;
    mockValidationService = TestBed.inject(ValidationService) as jasmine.SpyObj<ValidationService>;

    // Setup default mocks
    mockProbeConnectionHistoryService.getProbeConnectionHistoryListRefreshSubject.and.returnValue(new Subject<ListingPageReloadSubjectParameter>());
    mockProbeOperationService.getProbeTypesListFromCache.and.returnValue([]);
    mockProbeOperationService.getProbeTypesList.and.returnValue(Promise.resolve([]));
    mockMultiSelectDropDownSettingService.getProbeTypeDropdownSetting.and.returnValue({
      singleSelection: false,
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      itemsShowLimit: 3,
      allowSearchFilter: true,
      idField: 'id',
      textField: 'text',
      enableCheckAll: true,
      noDataAvailablePlaceholderText: 'No data available'
    });
    mockValidationService.removeSpaces = jasmine.createSpy('removeSpaces');
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component properties on ngOnInit', () => {
    component.ngOnInit();
    expect(mockMultiSelectDropDownSettingService.getProbeTypeDropdownSetting).toHaveBeenCalled();
    expect(component.dropdownSettingsForProbeType).toBeDefined();
  });

  it('should initialize with API call when isFilterComponentInitWithApicall is true', () => {
    component.isFilterComponentInitWithApicall = true;
    spyOn(component, 'clearFilter');
    component.ngOnInit();
    expect(component.clearFilter).toHaveBeenCalled();
  });

  it('should restore cached filter data when isFilterComponentInitWithApicall is false', () => {
    component.isFilterComponentInitWithApicall = false;
    spyOn(component, 'restoreCachedFilterData' as any);
    component.ngOnInit();
    expect((component as any).restoreCachedFilterData).toHaveBeenCalled();
  });

  it('should unsubscribe on ngOnDestroy', () => {
    const mockSubscription = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    component.subscriptionForRefeshList = mockSubscription;
    component.ngOnDestroy();
    expect(mockSubscription.unsubscribe).toHaveBeenCalled();
  });

  it('should handle ngOnDestroy when subscription is undefined', () => {
    component.subscriptionForRefeshList = undefined;
    expect(() => component.ngOnDestroy()).not.toThrow();
  });

  it('should initialize subject subscription', () => {
    const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
    mockProbeConnectionHistoryService.getProbeConnectionHistoryListRefreshSubject.and.returnValue(mockSubject);

    component.onInitSubject();
    expect(mockProbeConnectionHistoryService.getProbeConnectionHistoryListRefreshSubject).toHaveBeenCalled();
  });

  it('should handle subject subscription with clear filter', () => {
    const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
    mockProbeConnectionHistoryService.getProbeConnectionHistoryListRefreshSubject.and.returnValue(mockSubject);
    spyOn(component, 'clearFilter');

    component.onInitSubject();

    const testParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
    mockSubject.next(testParameter);

    expect(component.clearFilter).toHaveBeenCalledWith(testParameter);
  });

  it('should handle subject subscription without clear filter', () => {
    const mockSubject = new Subject<ListingPageReloadSubjectParameter>();
    mockProbeConnectionHistoryService.getProbeConnectionHistoryListRefreshSubject.and.returnValue(mockSubject);
    spyOn(component, 'probeConnectionHistoryListPageRefresh' as any);

    component.onInitSubject();

    const testParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    mockSubject.next(testParameter);

    expect((component as any).probeConnectionHistoryListPageRefresh).toHaveBeenCalledWith(testParameter);
  });

  it('should get filter list from cache when available', async () => {
    mockProbeOperationService.getProbeTypesListFromCache.and.returnValue(['type1', 'type2']);
    spyOn(component, 'setFilterValue' as any);

    await component.getFilterList();

    expect(mockProbeOperationService.getProbeTypesListFromCache).toHaveBeenCalled();
    expect(component.probeTypesList).toEqual(['type1', 'type2']);
    expect((component as any).setFilterValue).toHaveBeenCalled();
  });

  it('should get filter list from API when cache is empty', async () => {
    mockProbeOperationService.getProbeTypesListFromCache.and.returnValue([]);
    mockProbeOperationService.getProbeTypesList.and.returnValue(Promise.resolve(['type1', 'type2']));
    spyOn(component, 'setFilterValue' as any);

    await component.getFilterList();

    expect(mockProbeOperationService.getProbeTypesList).toHaveBeenCalled();
    expect(component.probeTypesList).toEqual(['type1', 'type2']);
    expect((component as any).setFilterValue).toHaveBeenCalled();
  });

  it('should set filter values when probeConnectionHistorySearchRequestBody is not null', () => {
    const mockSearchRequestBody = new ProbeConnectionHistorySearchRequestBody(
      'partNumber',
      ProbeTypeEnum.TORSO1,
      'serialNumber',
      new Date('2023-01-01').getTime(),
      new Date('2023-12-31').getTime()
    );
    component.probeConnectionHistorySearchRequestBody = mockSearchRequestBody;

    (component as any).setFilterValue();

    expect(component.filterProbeConnectionHistoryForm.get('serialNumber')?.value).toBe('serialNumber');
    expect(component.filterProbeConnectionHistoryForm.get('partNumber')?.value).toBe('partNumber');
    expect(component.filterProbeConnectionHistoryForm.get('probeType')?.value).toBe('Torso1');
  });

  it('should handle setFilterValue when probeConnectionHistorySearchRequestBody is null', () => {
    component.probeConnectionHistorySearchRequestBody = null;

    expect(() => (component as any).setFilterValue()).not.toThrow();
  });

  it('should call probeConnectionHistoryListPageRefresh when listPageRefreshForbackToDetailPage is true', () => {
    component.listPageRefreshForbackToDetailPage = true;
    spyOn(component, 'probeConnectionHistoryListPageRefresh' as any);

    (component as any).setFilterValue();

    expect((component as any).probeConnectionHistoryListPageRefresh).toHaveBeenCalled();
  });

  it('should restore cached filter data', () => {
    const mockCachedData = new ProbeConnectionHistorySearchRequestBody('part', ProbeTypeEnum.TORSO1, 'serial', null, null);
    mockProbeConnectionHistoryService.getLastAppliedProbeConnectionHistorySearchRequest.and.returnValue(mockCachedData);
    spyOn(component, 'setFilterValue' as any);

    (component as any).restoreCachedFilterData();

    expect(component.probeConnectionHistorySearchRequestBody).toBe(mockCachedData);
    expect((component as any).setFilterValue).toHaveBeenCalled();
  });

  it('should handle restoreCachedFilterData when no cached data exists', () => {
    mockProbeConnectionHistoryService.getLastAppliedProbeConnectionHistorySearchRequest.and.returnValue(null);

    expect(() => (component as any).restoreCachedFilterData()).not.toThrow();
  });

  it('should search data successfully', () => {
    mockProbeConnectionHistoryService.processFilterSearch.and.returnValue(true);
    mockProbeConnectionHistoryService.buildProbeConnectionHistoryFilterRequestBody.and.returnValue(new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null));

    component.searchData();

    expect(mockProbeConnectionHistoryService.processFilterSearch).toHaveBeenCalled();
    expect(mockProbeConnectionHistoryService.buildProbeConnectionHistoryFilterRequestBody).toHaveBeenCalled();
    expect(mockProbeConnectionHistoryService.setLastAppliedProbeConnectionHistorySearchRequest).toHaveBeenCalled();
  });

  it('should return early when search validation fails', () => {
    mockProbeConnectionHistoryService.processFilterSearch.and.returnValue(false);

    component.searchData();

    expect(mockProbeConnectionHistoryService.processFilterSearch).toHaveBeenCalled();
    expect(mockProbeConnectionHistoryService.buildProbeConnectionHistoryFilterRequestBody).not.toHaveBeenCalled();
  });

  it('should call searchData when searchFilteredProbeConnectionHistory is called', () => {
    spyOn(component, 'searchData');

    component.searchFilteredProbeConnectionHistory();

    expect(component.searchData).toHaveBeenCalled();
  });

  it('should clear filter with default parameter', () => {
    spyOn(component, 'clearAllFilter' as any);

    component.clearFilter();

    expect((component as any).clearAllFilter).toHaveBeenCalled();
    expect(mockProbeConnectionHistoryService.setLastAppliedProbeConnectionHistorySearchRequest).toHaveBeenCalledWith(null);
    expect(mockProbeConnectionHistoryService.clearAllFiltersAndRefresh).toHaveBeenCalled();
  });

  it('should clear filter with provided parameter', () => {
    const testParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
    spyOn(component, 'clearAllFilter' as any);

    component.clearFilter(testParameter);

    expect((component as any).clearAllFilter).toHaveBeenCalled();
    expect(mockProbeConnectionHistoryService.setLastAppliedProbeConnectionHistorySearchRequest).toHaveBeenCalledWith(null);
    expect(mockProbeConnectionHistoryService.clearAllFiltersAndRefresh).toHaveBeenCalledWith(testParameter);
  });

  it('should clear all filter form values', () => {
    component.filterProbeConnectionHistoryForm.get('serialNumber')?.setValue('test');
    component.filterProbeConnectionHistoryForm.get('partNumber')?.setValue('test');
    component.filterProbeConnectionHistoryForm.get('probeType')?.setValue('test');
    component.filterProbeConnectionHistoryForm.get('fromLastConnectedDate')?.setValue(new Date());
    component.filterProbeConnectionHistoryForm.get('toLastConnectedDate')?.setValue(new Date());

    (component as any).clearAllFilter();

    expect(component.filterProbeConnectionHistoryForm.get('serialNumber')?.value).toBeNull();
    expect(component.filterProbeConnectionHistoryForm.get('partNumber')?.value).toBeNull();
    expect(component.filterProbeConnectionHistoryForm.get('probeType')?.value).toBeNull();
    expect(component.filterProbeConnectionHistoryForm.get('fromLastConnectedDate')?.value).toBeNull();
    expect(component.filterProbeConnectionHistoryForm.get('toLastConnectedDate')?.value).toBeNull();
  });

  it('should handle probeConnectionHistoryListPageRefresh with invalid form', () => {
    component.filterProbeConnectionHistoryForm.setErrors({ invalid: true });
    spyOn(component.filterProbeConnectionHistoryForm, 'reset');

    const testParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    (component as any).probeConnectionHistoryListPageRefresh(testParameter);

    expect(component.filterProbeConnectionHistoryForm.reset).toHaveBeenCalled();
  });

  it('should handle probeConnectionHistoryListPageRefresh with valid form', () => {
    const testParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    mockProbeConnectionHistoryService.buildProbeConnectionHistoryFilterRequestBody.and.returnValue(new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null));

    (component as any).probeConnectionHistoryListPageRefresh(testParameter);

    expect(mockProbeConnectionHistoryService.buildProbeConnectionHistoryFilterRequestBody).toHaveBeenCalled();
    expect(mockProbeConnectionHistoryService.setLastAppliedProbeConnectionHistorySearchRequest).toHaveBeenCalled();
    expect(mockProbeConnectionHistoryService.callProbeConnectionHistoryListFilterRequestParameterSubject).toHaveBeenCalled();
  });
});
