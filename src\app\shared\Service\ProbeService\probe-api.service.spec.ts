import { DatePipe } from '@angular/common';
import { HttpTestingController } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ConfigMappingRequest } from 'src/app/model/probe/ConfigMappingRequest.model';
import { AddUpdateMultiProbeResponse } from 'src/app/model/probe/multiProbe/AddUpdateMultiProbeResponse.model';
import { ProbeFeatureRequest } from 'src/app/model/probe/multiProbe/ProbeFeatureRequest.model';
import { ProbeDetailWithConfig } from 'src/app/model/probe/ProbeDetailWithConfig.model';
import { ProbeDownloadCSVParameterRequest } from 'src/app/model/probe/ProbeDownloadCSVParameterRequest.model';
import { ProbeDownloadCSVRequest } from 'src/app/model/probe/ProbeDownloadCSVRequest.model';
import { ProbeHistoryPagableResponse } from 'src/app/model/probe/ProbeHistoryPagableResponse.model';
import { BASE_URL, commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ExceptionHandlingService } from '../../ExceptionHandling.service';
import { ConfigInjectService } from '../../InjectService/config-inject.service';
import { CommonsService } from '../../util/commons.service';
import { DownloadService } from '../../util/download.service';
import { ProbeApiService } from './probe-api.service';

describe('ProbeApiService', () => {
  let service: ProbeApiService;
  let httpMock: HttpTestingController;
  let toastrServiceMock: any;
  let exceptionServiceMock: any;
  let commonsServiceMock: any;
  let downloadServiceMock: any;

  beforeEach(() => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning']);
    exceptionServiceMock = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    commonsServiceMock = jasmine.createSpyObj('CommonsService', ['handleError', 'checkValueIsNullOrEmpty']);
    downloadServiceMock = jasmine.createSpyObj('DownloadService', ['setLoading', 'dowloadInZipFile']);

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        DatePipe,
        ConfigInjectService,
        { provide: ToastrService, useValue: toastrServiceMock },
        { provide: ExceptionHandlingService, useValue: exceptionServiceMock },
        { provide: CommonsService, useValue: commonsServiceMock },
        { provide: DownloadService, useValue: downloadServiceMock },
        commonsProviders(null)
      ]
    });

    service = TestBed.inject(ProbeApiService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('#getprobeTypeResponseList', () => {
    it('should return types and add default if requested', async () => {
      const mockResponse = [{ id: 1, name: 'Test' }];
      const request = service.getprobeTypeResponseList(true).then(res => {
        expect(res.length).toBe(2); // 1 default + 1 from backend
      });

      const req = httpMock.expectOne(`${BASE_URL}api/probes/types/config`);
      req.flush(mockResponse);

      await request;
    });

    it('should return empty on error', async () => {
      const request = service.getprobeTypeResponseList(false).then(res => {
        expect(res).toEqual([]);
        expect(exceptionServiceMock.customErrorMessage).toHaveBeenCalled();
      });

      const req = httpMock.expectOne(`${BASE_URL}api/probes/types/config`);
      req.flush({}, { status: 500, statusText: 'Server Error' });

      await request;
    });
  });

  describe('#getFeaturesList', () => {
    it('should return feature list on success', async () => {
      const mockFeatures = null;
      const result = service.getFeaturesList().then(res => {
        expect(res).toEqual(mockFeatures);
      });

      const req = httpMock.expectOne(`${BASE_URL}api/probes/features`);
      req.flush(mockFeatures);

      await result;
    });

    it('should handle error and return empty list', async () => {
      const result = service.getFeaturesList().then(res => {
        expect(res).toEqual([]);
        expect(exceptionServiceMock.customErrorMessage).toHaveBeenCalled();
      });

      const req = httpMock.expectOne(`${BASE_URL}api/probes/features`);
      req.flush({}, { status: 500, statusText: 'Internal Server Error' });

      await result;
    });
  });

  describe('#getAsyncProbeDetailInfo', () => {
    it('should return detail info', async () => {
      const mockDetail = null;
      const result = service.getAsyncProbeDetailInfo(123).then(res => {
        expect(res).toEqual(mockDetail);
      });

      const req = httpMock.expectOne(`${BASE_URL}api/probes/123`);
      req.flush(mockDetail);

      await result;
    });

    it('should handle error and return null', async () => {
      const result = service.getAsyncProbeDetailInfo(123).then(res => {
        expect(res).toBeNull();
        expect(exceptionServiceMock.customErrorMessage).toHaveBeenCalled();
      });

      const req = httpMock.expectOne(`${BASE_URL}api/probes/123`);
      req.flush({}, { status: 404, statusText: 'Not Found' });

      await result;
    });
  });

  describe('#updateLockState', () => {
    it('should update lock state successfully', async () => {
      const mockResponse = { body: { message: 'Success' }, status: 200 };
      const result = service.updateLockState([1, 2], true).then(res => {
        expect(res).toBeFalsy();
      });

      const req = httpMock.expectOne(`${BASE_URL}api/probes/state/1,2?locked=true`);
      req.flush(mockResponse);

      await result;
    });

    it('should handle error and return false', async () => {
      const result = service.updateLockState([1], false).then(res => {
        expect(res).toBeFalse();
        expect(exceptionServiceMock.customErrorMessage).toHaveBeenCalled();
      });

      const req = httpMock.expectOne(`${BASE_URL}api/probes/state/1?locked=false`);
      req.flush({}, { status: 500, statusText: 'Error' });

      await result;
    });
  });

  it('should call getAllProbes and return data', () => {
    const mockResponse = null;
    service.getAllProbes(null, null).subscribe(response => {
      expect(response.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(each => each.method === 'POST' && each.url.includes('/api/probes/search'));
    req.flush(mockResponse);
  });

  it('should call associationProbeWithSalesOrder and return success', () => {
    const mockResponse = { message: 'success' };
    service.associationProbeWithSalesOrder([1, 2], { orderId: '123' } as any).subscribe(res => {
      expect(res.body.message).toEqual('success');
    });

    const req = httpMock.expectOne(`${BASE_URL}api/probes/soDetails/1,2`);
    req.flush(mockResponse);
  });

  it('should update probe type and return success', () => {
    const mockResponse = { message: 'updated' };
    service.probeTypeUpdate([1, 2], { type: 'NewType' } as any).subscribe(res => {
      expect(res.body.message).toBe('updated');
    });

    const req = httpMock.expectOne(each => each.url.includes(`${BASE_URL}api/probes/type/1,2`));
    req.flush(mockResponse);
  });

  it('should disable product status and return success', () => {
    const mockResponse = { message: 'disabled' };
    service.disableProductStatusForProbe([1, 2]).subscribe(res => {
      expect(res.body.message).toBe('disabled');
    });

    const req = httpMock.expectOne(`${BASE_URL}api/probes/status/disable/1,2`);
    req.flush(mockResponse);
  });

  it('should set RMA status and return success', () => {
    const mockResponse = { message: 'rma set' };
    service.rmaProductStatusForProbe([1, 2]).subscribe(res => {
      expect(res.body.message).toBe('rma set');
    });

    const req = httpMock.expectOne(`${BASE_URL}api/probes/status/rma/1,2`);
    req.flush(mockResponse);
  });

  it('should enable/disable probes and return success', () => {
    const mockResponse = { message: 'status updated' };
    service.editEnableDisableProbe([1], true).subscribe(res => {
      expect(res.body.message).toBe('status updated');
    });

    const req = httpMock.expectOne(`${BASE_URL}api/probes/edit/1?enable=true`);
    req.flush(mockResponse);
  });

  it('should delete probes and return success', () => {
    const mockResponse = { message: 'deleted' };
    service.deleteProbes([1, 2]).subscribe(res => {
      expect(res.body.message).toBe('deleted');
    });

    const req = httpMock.expectOne(`${BASE_URL}api/probes/1,2`);
    req.flush(mockResponse);
  });

  it('should fetch SAS URI for license', () => {
    const mockResponse = { sasUri: 'https://file.link' };
    service.getSasUriofFeatureLicense([1, 2]).subscribe(res => {
    });

    const req = httpMock.expectOne(`${BASE_URL}api/probes/1,2/license/download`);
    req.flush(mockResponse);
  });

  it('should download sales order pdf letter', () => {
    const mockResponse = { pdfUrl: 'somefile.pdf' };
    service.downloadSalesOrderPdfLetter('SO123').subscribe(res => {
    });

    const req = httpMock.expectOne(`${BASE_URL}api/probes/pdf/SO123`);
    req.flush(mockResponse);
  });



  it('should save multiprobe and return response', () => {
    const requestPayload: ProbeFeatureRequest = null;

    const mockResponse: AddUpdateMultiProbeResponse = null;

    service.saveMutiprobe(requestPayload).subscribe(response => {
      expect(response.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${service['probesUrl']}`);
    expect(req.request.method).toBe('POST');
    expect(req.request.body).toEqual(requestPayload);

    req.flush(mockResponse);
  });

  it('should fetch probe detail info by ID', () => {
    const probeId = 123;
    const mockResponse: ProbeDetailWithConfig = null;

    service.getProbeDetailInfo(probeId).subscribe(response => {
      expect(response.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${service['probesUrl']}/${probeId}`);
    expect(req.request.method).toBe('GET');

    req.flush(mockResponse);
  });

  it('should update probe features and return a success message response', () => {
    const probeId = 101;
    const configMappingRequest: ConfigMappingRequest = null;
    const mockResponse: SuccessMessageResponse = {
      message: 'Probe features updated successfully'
    };

    service.updateProbeFeatures(probeId, configMappingRequest).subscribe(response => {
      expect(response.body).toEqual(mockResponse);
      expect(response.status).toBe(200);
    });

    const req = httpMock.expectOne(`${service['probesUrl']}/license/${probeId}`);
    expect(req.request.method).toBe('PUT');
    expect(req.request.body).toEqual(configMappingRequest);

    req.flush(mockResponse);
  });

  it('should fetch probe history with query params and return paginated response', () => {
    const probeId = 123;
    const requestParams = { page: 0, size: 10 };
    const mockResponse: ProbeHistoryPagableResponse = null;

    service.getProbeHistory(probeId, requestParams).subscribe(response => {
      expect(response.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(
      r => r.method === 'GET' && r.url === `${service['probesUrl']}/${probeId}/config-history`
    );
    expect(req.request.params.get('page')).toBe('0');
    expect(req.request.params.get('size')).toBe('10');

    req.flush(mockResponse);
  });

  it('should generate CSV file for probe with query params and post body', () => {
    const downloadRequest: ProbeDownloadCSVRequest = null;
    const paramRequest: ProbeDownloadCSVParameterRequest = null;

    const mockResponse = new Blob(['col1,col2\nval1,val2'], { type: 'text/csv' });

    service.generateCSVFileForProbe(downloadRequest, paramRequest).subscribe(response => {
      expect(response.body).toEqual(mockResponse);
      expect(response.status).toBe(200);
    });

    const req = httpMock.expectOne(
      r => r.method === 'POST' &&
        r.url === `${service['probesUrl']}/generateCSV`
    );
    expect(req.request.body).toEqual(downloadRequest);
  });

  it('should download the CSV file for probe', () => {
    const mockFileName = 'probeData.csv';
    const mockCSVResponse = new Blob(['header1,header2\nvalue1,value2'], { type: 'text/csv' });

    service.downloadCSVFileForProbe(mockFileName).subscribe(response => {
      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockCSVResponse);
    });

    const req = httpMock.expectOne(`${service['probesUrl']}/downloadProbesData/${mockFileName}`);
    expect(req.request.method).toBe('GET');
    expect(req.request.responseType).toBe('blob');

  });

  it('should get the list of probe types', () => {
    const mockResponse = [
      { id: 1, name: 'ProbeType1' },
      { id: 2, name: 'ProbeType2' },
    ];

    service.getProbeTypesList().subscribe(response => {
      expect(response.status).toBe(200);
      expect(response.body).toEqual(mockResponse);
    });

    const req = httpMock.expectOne(`${service['probesUrl']}/types`);
    expect(req.request.method).toBe('GET');
    expect(req.request.responseType).toBe('json');

    req.flush(mockResponse, { status: 200, statusText: 'OK' });
  });

  it('should handle empty response (e.g., no SAS URI)', async () => {
    const probeIds = [1];
    const resource = 'feature-license';

    spyOn(console, 'warn');  // Simulate warning for empty SAS URI

    const promise = service.dowloadSasUriofFeatureLicenseAsync(probeIds, resource);

    const req = httpMock.expectOne(`${BASE_URL}api/probes/license/download/1`);
    // For empty response - specify the response type as text
    req.flush('', {
      status: 200,
      statusText: 'OK',
      headers: { 'Content-Type': 'text/plain' }
    });

    await expectAsync(promise).toBeResolved();
  });

  it('should handle HTTP error gracefully', async () => {
    const probeIds = [1];
    const resource = 'feature-license';

    const promise = service.dowloadSasUriofFeatureLicenseAsync(probeIds, resource);

    const req = httpMock.expectOne(`${BASE_URL}api/probes/license/download/1`);
    req.flush('Internal Server Error', {
      status: 500,
      statusText: 'Server Error',
      headers: { 'Content-Type': 'text/plain' }
    });

    await expectAsync(promise).toBeResolved();  // If method handles error internally
  });

  it('should return a list of presets on success', async () => {
    const mockResponse = null;
    const resultPromise = service.getPresetsList();

    const req = httpMock.expectOne(`${BASE_URL}api/probes/presets`);
    expect(req.request.method).toBe('GET');
    req.flush(mockResponse, { status: 200, statusText: 'OK' });

    const result = await resultPromise;
    expect(result).toEqual(mockResponse);
  });

  it('should return empty list and call customErrorMessage on error', async () => {
    const resultPromise = service.getPresetsList();

    const req = httpMock.expectOne(`${BASE_URL}api/probes/presets`);
    expect(req.request.method).toBe('GET');

    req.flush('Error loading presets', {
      status: 500,
      statusText: 'Internal Server Error'
    });

    const result = await resultPromise;
    expect(result).toEqual([]);
    expect(exceptionServiceMock.customErrorMessage).toHaveBeenCalled();
  });
});
