import { DeviceConnectionHistorySearchRequestBody } from "./DeviceConnectionHistorySearchRequestBody.model";

export class DeviceConnectionHistoryExportCSVSearchRequest {
    deviceConnectionHistoryIds: Array<number>;
    timezoneOffset: number;
    filters: DeviceConnectionHistorySearchRequestBody;
    
    constructor(
        $deviceConnectionHistoryIds: Array<number>, 
        $timezoneOffset: number, 
        $filters: DeviceConnectionHistorySearchRequestBody
    ) {
        this.deviceConnectionHistoryIds = $deviceConnectionHistoryIds;
        this.timezoneOffset = $timezoneOffset;
        this.filters = $filters;
    }
}
