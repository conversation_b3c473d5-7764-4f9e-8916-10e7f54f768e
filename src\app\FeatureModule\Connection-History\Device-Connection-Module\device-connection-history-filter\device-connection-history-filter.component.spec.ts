import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DeviceConnectionHistoryFilterComponent } from './device-connection-history-filter.component';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { DeviceConnectionHistoryOperationService } from '../Device-Connection-History-Service/Device-Connection-History-Operation/device-connection-history-operation-service.service';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { Subject } from 'rxjs';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistorySearchRequestBody.model';

describe('DeviceConnectionHistoryFilterComponent', () => {
  let component: DeviceConnectionHistoryFilterComponent;
  let fixture: ComponentFixture<DeviceConnectionHistoryFilterComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let deviceConnectionHistoryServiceSpy: jasmine.SpyObj<DeviceConnectionHistoryOperationService>;
  let keyValueMappingServiceSpy: jasmine.SpyObj<KeyValueMappingServiceService>;

  const mockSearchRequestBody = new DeviceConnectionHistorySearchRequestBody(
    'TestManufacturer',
    'TestModel',
    'TEST123',
    null,
    1672531200000
  );

  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    deviceConnectionHistoryServiceSpy = jasmine.createSpyObj('DeviceConnectionHistoryOperationService', [
      'getDeviceConnectionHistoryListRefreshSubject',
      'processFilterSearch',
      'buildDeviceConnectionHistoryFilterRequestBody',
      'setLastAppliedDeviceConnectionHistorySearchRequest',
      'getLastAppliedDeviceConnectionHistorySearchRequest',
      'clearAllFiltersAndRefresh',
      'callDeviceConnectionHistoryListFilterRequestParameterSubject'
    ]);
    keyValueMappingServiceSpy = jasmine.createSpyObj('KeyValueMappingServiceService', ['enumOptionToList']);

    keyValueMappingServiceSpy.enumOptionToList.and.returnValue([]);
    deviceConnectionHistoryServiceSpy.processFilterSearch.and.returnValue(true);
    deviceConnectionHistoryServiceSpy.buildDeviceConnectionHistoryFilterRequestBody.and.returnValue(mockSearchRequestBody);
    deviceConnectionHistoryServiceSpy.getLastAppliedDeviceConnectionHistorySearchRequest.and.returnValue(null);

    const mockRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();
    deviceConnectionHistoryServiceSpy.getDeviceConnectionHistoryListRefreshSubject.and.returnValue(mockRefreshSubject);

    await TestBed.configureTestingModule({
      declarations: [DeviceConnectionHistoryFilterComponent],
      imports: [
        MatFormFieldModule,
        MatDatepickerModule,
        BrowserAnimationsModule,
        MatNativeDateModule,
        MatInputModule,
        ReactiveFormsModule,
        FormsModule,
        NgMultiSelectDropDownModule.forRoot()
      ],
      providers: [
        MultiSelectDropDownSettingService,
        { provide: DeviceConnectionHistoryOperationService, useValue: deviceConnectionHistoryServiceSpy },
        CommonsService,
        { provide: KeyValueMappingServiceService, useValue: keyValueMappingServiceSpy },
        LocalStorageService,
        SessionStorageService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        PrintListPipe,
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DeviceConnectionHistoryFilterComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with API call', () => {
    component.isFilterComponentInitWithApicall = true;
    spyOn(component, 'clearFilter');
    component.ngOnInit();
    expect(component.clearFilter).toHaveBeenCalled();
  });

  it('should restore cached filter data', () => {
    component.isFilterComponentInitWithApicall = false;
    spyOn(component as any, 'restoreCachedFilterData');
    component.ngOnInit();
    expect((component as any).restoreCachedFilterData).toHaveBeenCalled();
  });

  it('should handle refresh list subscription', () => {
    spyOn(component, 'clearFilter');
    fixture.detectChanges();
    const refreshSubject = deviceConnectionHistoryServiceSpy.getDeviceConnectionHistoryListRefreshSubject();
    const mockParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
    refreshSubject.next(mockParameter);
    expect(component.clearFilter).toHaveBeenCalledWith(mockParameter);
  });

  it('should unsubscribe on destroy', () => {
    fixture.detectChanges();
    const subscriptionSpy = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    component['subscriptionForRefeshList'] = subscriptionSpy;
    component.ngOnDestroy();
    expect(subscriptionSpy.unsubscribe).toHaveBeenCalled();
  });

  it('should set filter values', () => {
    component.deviceConnectionHistorySearchRequestBody = mockSearchRequestBody;
    (component as any).setFilterValue();
    expect(component.filterDeviceConnectionHistoryForm.get('serialNumber')?.value).toBe('TEST123');
  });

  it('should search data successfully', () => {
    const formValue = { serialNumber: 'TEST123' };
    component.filterDeviceConnectionHistoryForm.patchValue(formValue);
    component.searchData();
    expect(deviceConnectionHistoryServiceSpy.processFilterSearch).toHaveBeenCalled();
  });

  it('should clear filter', () => {
    spyOn(component as any, 'clearAllFilter');
    component.clearFilter();
    expect((component as any).clearAllFilter).toHaveBeenCalled();
    expect(deviceConnectionHistoryServiceSpy.setLastAppliedDeviceConnectionHistorySearchRequest).toHaveBeenCalledWith(null);
  });

  it('should clear all filter form values', () => {
    component.filterDeviceConnectionHistoryForm.patchValue({ serialNumber: 'test' });
    (component as any).clearAllFilter();
    expect(component.filterDeviceConnectionHistoryForm.get('serialNumber')?.value).toBeNull();
  });

  it('should call searchFilteredDeviceConnectionHistory', () => {
    spyOn(component, 'searchData');
    component.searchFilteredDeviceConnectionHistory();
    expect(component.searchData).toHaveBeenCalled();
  });

  it('should get filter list', async () => {
    spyOn(component as any, 'setFilterValue');
    await (component as any).getFilterList();
    expect(keyValueMappingServiceSpy.enumOptionToList).toHaveBeenCalled();
  });

  it('should restore cached filter data with existing data', () => {
    deviceConnectionHistoryServiceSpy.getLastAppliedDeviceConnectionHistorySearchRequest.and.returnValue(mockSearchRequestBody);
    spyOn(component as any, 'setFilterValue');
    (component as any).restoreCachedFilterData();
    expect(component.deviceConnectionHistorySearchRequestBody).toEqual(mockSearchRequestBody);
    expect((component as any).setFilterValue).toHaveBeenCalled();
  });

  it('should handle null cached filter data', () => {
    deviceConnectionHistoryServiceSpy.getLastAppliedDeviceConnectionHistorySearchRequest.and.returnValue(null);
    spyOn(component as any, 'setFilterValue');
    (component as any).restoreCachedFilterData();
    expect((component as any).setFilterValue).not.toHaveBeenCalled();
  });

  it('should refresh page with valid form', () => {
    const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const formValue = { serialNumber: 'TEST123' };
    component.filterDeviceConnectionHistoryForm.patchValue(formValue);
    (component as any).deviceConnectionHistoryListPageRefresh(mockParameter);
    expect(deviceConnectionHistoryServiceSpy.buildDeviceConnectionHistoryFilterRequestBody).toHaveBeenCalled();
  });
});
