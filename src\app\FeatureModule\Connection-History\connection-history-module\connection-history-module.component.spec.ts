import { ComponentFixture, TestBed } from '@angular/core/testing';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { ConnectionHistoryModuleComponent } from './connection-history-module.component';

describe('ConnectionHistoryModuleComponent', () => {
  let component: ConnectionHistoryModuleComponent;
  let fixture: ComponentFixture<ConnectionHistoryModuleComponent>;
  let authServiceMock: jasmine.SpyObj<AuthJwtService>;
  let permissionServiceMock: jasmine.SpyObj<PermissionService>;

  beforeEach(async () => {
    authServiceMock = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    permissionServiceMock = jasmine.createSpyObj('PermissionService', ['getConnectionHistoryPermission']);

    await TestBed.configureTestingModule({
      declarations: [ConnectionHistoryModuleComponent],
      providers: [
        { provide: LocalStorageService, useValue: jasmine.createSpyObj('LocalStorageService', ['retrieve']) },
        SessionStorageService,
        { provide: AuthJwtService, useValue: authServiceMock },
        { provide: PermissionService, useValue: permissionServiceMock },
        commonsProviders(null)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ConnectionHistoryModuleComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should navigate to login if not authenticated', () => {
    authServiceMock.isAuthenticate.and.returnValue(false);

    component.ngOnInit();

    expect(authServiceMock.loginNavigate).toHaveBeenCalled();
    expect(permissionServiceMock.getConnectionHistoryPermission).not.toHaveBeenCalled();
  });

  it('should set permission and show device history when authenticated and has permission', () => {
    authServiceMock.isAuthenticate.and.returnValue(true);
    permissionServiceMock.getConnectionHistoryPermission.and.returnValue(true);

    component.ngOnInit();

    expect(component.connectionHistoryDisplayPermissions).toBeTrue();
    expect(component.deviceConnectionHistoryListDisplay).toBeTrue();
    expect(component.probeConnectionHistoryListDisplay).toBeFalse();
  });

  it('should not show device history when authenticated but has no permission', () => {
    authServiceMock.isAuthenticate.and.returnValue(true);
    permissionServiceMock.getConnectionHistoryPermission.and.returnValue(false);

    component.ngOnInit();

    expect(component.connectionHistoryDisplayPermissions).toBeFalse();
    expect(component.deviceConnectionHistoryListDisplay).toBeFalse();
    expect(component.probeConnectionHistoryListDisplay).toBeFalse();
  });

  it('should show and hide pages correctly', () => {
    component.showDeviceConnectionHistoryListDisplay();
    expect(component.deviceConnectionHistoryListDisplay).toBeTrue();
    expect(component.probeConnectionHistoryListDisplay).toBeFalse();

    component.showProbeConnectionHistoryListDisplay();
    expect(component.deviceConnectionHistoryListDisplay).toBeFalse();
    expect(component.probeConnectionHistoryListDisplay).toBeTrue();
  });
});
