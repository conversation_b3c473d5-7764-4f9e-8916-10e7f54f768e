import { Component, Input } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isNullOrUndefined, isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { DEVICE_CONNECTION_HISTORY_LAST_CONNECTED_FROM, DEVICE_CONNECTION_HISTORY_LAST_CONNECTED_TO, FILTER_CLEAR_BUTTON, FILTER_SEARCH_BUTTON, PROBE_CONNECTION_HISTORY_LAST_CONNECTED_FROM, PROBE_CONNECTION_HISTORY_PART_NUMBER, PROBE_CONNECTION_HISTORY_PROBE_TYPE, PROBE_CONNECTION_HISTORY_SERIAL_NUMBER_OR_HW_ID, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_ERROR_MESSAGE, SPECIAL_CHARACTER_PATTERN } from 'src/app/app.constants';
import { ProbeOperationService } from 'src/app/FeatureModule/Probe/ProbeService/Probe-Operation/probe-operation.service';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ProbeConnectionHistoryFilterAction } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistoryFilterAction.model';
import { ProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistorySearchRequestBody.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { ProbeConnectionHistoryOperationService } from '../probe-connection-history-services/probe-connection-history-operation/probe-connection-history-operation.service';

@Component({
  selector: 'app-probe-connection-history-filter',
  templateUrl: './probe-connection-history-filter.component.html',
  styleUrl: './probe-connection-history-filter.component.css'
})
export class ProbeConnectionHistoryFilterComponent {
  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean;
  @Input("probeConnectionHistorySearchRequestBody") probeConnectionHistorySearchRequestBody: ProbeConnectionHistorySearchRequestBody;

  //MaxLength Message
  textBoxMaxLengthMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;

  //maxDate
  maxdate: Date = new Date();

  //Constance
  serialNumberOrHwId: string = PROBE_CONNECTION_HISTORY_SERIAL_NUMBER_OR_HW_ID;
  partNumber: string = PROBE_CONNECTION_HISTORY_PART_NUMBER;
  probeType: string = PROBE_CONNECTION_HISTORY_PROBE_TYPE;
  lastConnectedFrom: string = DEVICE_CONNECTION_HISTORY_LAST_CONNECTED_FROM;
  lastConnectedTo: string = DEVICE_CONNECTION_HISTORY_LAST_CONNECTED_TO;

  searchBtnText: string = FILTER_SEARCH_BUTTON;
  clearBtnText: string = FILTER_CLEAR_BUTTON;

  filterProbeConnectionHistoryForm = new FormGroup({
    serialNumber: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    partNumber: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    probeType: new FormControl(null, []),
    fromLastConnectedDate: new FormControl(null, []),
    toLastConnectedDate: new FormControl(null, [])
  });

  subscriptionForRefeshList: Subscription;

  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

  dropdownSettingsForProbeType: MultiSelectDropdownSettings = null;

  probeTypesList: Array<any> = [];

  constructor(private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private probeConnectionHistoryService: ProbeConnectionHistoryOperationService,
    private probeOperationService: ProbeOperationService,
    private validationService: ValidationService,
  ) { }


  /**
  * On Init
  * <AUTHOR>
  */
  public ngOnInit(): void {
    this.dropdownSettingsForProbeType = this.multiSelectDropDownSettingService.getProbeTypeDropdownSetting();
    this.onInitSubject();
    this.getFilterList();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    } else {
      // When not initializing with API call, restore cached filter data
      this.restoreCachedFilterData();
    }
  }

  /**
   * Destroy 
   */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefeshList)) { this.subscriptionForRefeshList.unsubscribe() }
  }

  /**
  * Subject 
  * 
  * <AUTHOR>
  */
  public onInitSubject(): void {
    /**
    * Probe Connection History Refresh After some Action 
    * <AUTHOR>
    */
    this.subscriptionForRefeshList = this.probeConnectionHistoryService.getProbeConnectionHistoryListRefreshSubject().subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        if (listingPageReloadSubjectParameter.isClearFilter) {
          this.clearFilter(listingPageReloadSubjectParameter);
        } else {
          //page change 1,2,3
          this.probeConnectionHistoryListPageRefresh(listingPageReloadSubjectParameter);
        }
      }
    });
  }


  /**
  * Get Filter Value List
  *
  * <AUTHOR>
  */
  public async getFilterList(): Promise<void> {
    if (this.probeOperationService.getProbeTypesListFromCache().length === 0) {
      this.probeTypesList = await this.probeOperationService.getProbeTypesList();
    } else {
      this.probeTypesList = this.probeOperationService.getProbeTypesListFromCache();
    } this.setFilterValue();
  }

  /**
   * set Filter value
   */
  private setFilterValue() {
    if (this.probeConnectionHistorySearchRequestBody != null) {
      let fromLastConnectedDate = isNullOrUndefined(this.probeConnectionHistorySearchRequestBody.fromLastConnectedDate) ? null : new Date(this.probeConnectionHistorySearchRequestBody.fromLastConnectedDate);
      let toLastConnectedDate = isNullOrUndefined(this.probeConnectionHistorySearchRequestBody.toLastConnectedDate) ? null : new Date(this.probeConnectionHistorySearchRequestBody.toLastConnectedDate);


      this.filterProbeConnectionHistoryForm.get('serialNumber').setValue(this.probeConnectionHistorySearchRequestBody.probeSerialNumber);
      this.filterProbeConnectionHistoryForm.get('partNumber').setValue(this.probeConnectionHistorySearchRequestBody.probePartNumber);
      this.filterProbeConnectionHistoryForm.get('probeType').setValue(this.probeConnectionHistorySearchRequestBody.probeType);
      this.filterProbeConnectionHistoryForm.get('fromLastConnectedDate').setValue(fromLastConnectedDate)
      this.filterProbeConnectionHistoryForm.get('toLastConnectedDate').setValue(toLastConnectedDate);
    }
    if (this.listPageRefreshForbackToDetailPage) {
      this.probeConnectionHistoryListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
  * Restore cached filter data when component is initialized without API call
  * <AUTHOR>
  */
  private restoreCachedFilterData(): void {
    const cachedFilterData = this.probeConnectionHistoryService.getLastAppliedProbeConnectionHistorySearchRequest();
    if (cachedFilterData) {
      this.probeConnectionHistorySearchRequestBody = cachedFilterData;
      this.setFilterValue();
    }
  }

  /**
  * Search Data
  * Cache filter data for persistence when hiding/showing filters
  * <AUTHOR>
  */
  public searchData(): void {
    const allFormValue = this.filterProbeConnectionHistoryForm.value;

    const searchProcessed = this.probeConnectionHistoryService.processFilterSearch(allFormValue, this.filterProbeConnectionHistoryForm.invalid, this.defaultListingPageReloadSubjectParameter);

    if (!searchProcessed) {
      // Validation failed - message already shown by service
      return;
    }

    // Cache the filter data for persistence
    const probeConnectionHistorySearchRequestBody = this.probeConnectionHistoryService.buildProbeConnectionHistoryFilterRequestBody(allFormValue);
    this.probeConnectionHistoryService.setLastAppliedProbeConnectionHistorySearchRequest(probeConnectionHistorySearchRequestBody);
  }

  /**
  * Search filtered probe connection history
  * <AUTHOR>
  */
  public searchFilteredProbeConnectionHistory(): void {
    this.searchData();
  }

  /**
  * Clear All filter and search api call
  * Clear cached filter data for persistence
  * <AUTHOR>
  *
  * @param listingPageReloadSubjectParameter
  */
  public clearFilter(listingPageReloadSubjectParameter?: ListingPageReloadSubjectParameter): void {
    this.clearAllFilter();

    // Clear cached filter data
    this.probeConnectionHistoryService.setLastAppliedProbeConnectionHistorySearchRequest(null);

    const reloadParameter = listingPageReloadSubjectParameter || this.defaultListingPageReloadSubjectParameter;
    this.probeConnectionHistoryService.clearAllFiltersAndRefresh(reloadParameter);
  }

  /**
  * Clear all filter form values
  * <AUTHOR>
  */
  private clearAllFilter(): void {
    this.filterProbeConnectionHistoryForm.get('serialNumber').setValue(null);
    this.filterProbeConnectionHistoryForm.get('partNumber').setValue(null);
    this.filterProbeConnectionHistoryForm.get('probeType').setValue(null);
    this.filterProbeConnectionHistoryForm.get('fromLastConnectedDate').setValue(null);
    this.filterProbeConnectionHistoryForm.get('toLastConnectedDate').setValue(null);
  }

  /**
  * Send filter data to listing component and reload page
  * Cache filter data for persistence when hiding/showing filters
  * <AUTHOR>
  */
  private probeConnectionHistoryListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterProbeConnectionHistoryForm.invalid) {
      this.filterProbeConnectionHistoryForm.reset();
    }

    const probeConnectionHistorySearchRequestBody = this.probeConnectionHistoryService.buildProbeConnectionHistoryFilterRequestBody(this.filterProbeConnectionHistoryForm.value);

    // Cache the filter data for persistence
    this.probeConnectionHistoryService.setLastAppliedProbeConnectionHistorySearchRequest(probeConnectionHistorySearchRequestBody);

    const probeConnectionHistoryFilterAction = new ProbeConnectionHistoryFilterAction(listingPageReloadSubjectParameter, probeConnectionHistorySearchRequestBody);
    this.probeConnectionHistoryService.callProbeConnectionHistoryListFilterRequestParameterSubject(probeConnectionHistoryFilterAction);
  }
}
