import { OSTypeEnum } from "src/app/shared/enum/Probe/OSTypeEnum.enum";

export class DeviceConnectionHistorySearchRequestBody {
    manufacturer?: string;
    deviceModel?: string;
    deviceSerialNumber?: string;
    osType?: OSTypeEnum;
    fromLastConnectedDate?: number;
    toLastConnectedDate?: number;

    constructor(manufacturer?: string, deviceModel?: string, deviceSerialNumber?: string, osType?: OSTypeEnum, fromLastConnectedDate?: number, toLastConnectedDate?: number) {
        this.manufacturer = manufacturer;
        this.deviceModel = deviceModel;
        this.deviceSerialNumber = deviceSerialNumber;
        this.osType = osType;
        this.fromLastConnectedDate = fromLastConnectedDate;
        this.toLastConnectedDate = toLastConnectedDate;
    }
}