import { DeviceProbeConnectionHistorySearchRequestBody } from "./DeviceProbeConnectionHistorySearchRequestBody.model";

export class DeviceProbeConnectionHistoryExportCSVSearchRequest {
    deviceConnectionHistoryId: number;
    timezoneOffset: number;
    filters: DeviceProbeConnectionHistorySearchRequestBody;
    
    constructor(
        $deviceConnectionHistoryId: number, 
        $timezoneOffset: number, 
        $filters: DeviceProbeConnectionHistorySearchRequestBody
    ) {
        this.deviceConnectionHistoryId = $deviceConnectionHistoryId;
        this.timezoneOffset = $timezoneOffset;
        this.filters = $filters;
    }
}
