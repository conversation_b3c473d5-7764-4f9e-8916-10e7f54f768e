import { ProbeTypeEnum } from "src/app/shared/enum/ProbeType.enum";

export class ProbeConnectionHistorySearchRequestBody {
    probePartNumber?: string;
    probeType: ProbeTypeEnum;
    probeSerialNumber?: string;
    fromLastConnectedDate?: number;
    toLastConnectedDate?: number;

    constructor(probePartNumber?: string, probeType?: ProbeTypeEnum, probeSerialNumber?: string, fromLastConnectedDate?: number, toLastConnectedDate?: number) {
        this.probePartNumber = probePartNumber;
        this.probeType = probeType;
        this.probeSerialNumber = probeSerialNumber;
        this.fromLastConnectedDate = fromLastConnectedDate;
        this.toLastConnectedDate = toLastConnectedDate;
    }
}