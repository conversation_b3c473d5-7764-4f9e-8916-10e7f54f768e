import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, Observable } from 'rxjs';
import { DeviceConnectionHistoryDetails } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryDetails.model';
import { DeviceConnectionHistoryPegableResponse } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryPegableResponse.mode';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistorySearchRequestBody.model';
import { DeviceConnectionHistoryExportCSVSearchRequest } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryExportCSVSearchRequest.model';
import { DeviceProbeConnectionHistoryExportCSVSearchRequest } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistoryExportCSVSearchRequest.model';
import { DeviceProbeConnectionHistoryPagableResponse } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistoryPagableResponse.model';
import { DeviceProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistorySearchRequestBody.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ConfigInjectService } from 'src/app/shared/InjectService/config-inject.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { createRequestOption } from 'src/app/shared/util/request-util';

@Injectable({
  providedIn: 'root'
})
export class DeviceConnectionApiCallService {

  public deviceConnectionHistory = this.configInjectService.getServerApiUrl() + 'api/connection-history';

  constructor(
    private configInjectService: ConfigInjectService,
    private http: HttpClient,
    private exceptionService: ExceptionHandlingService,
    private commonsService: CommonsService,
  ) { }

  /**
  * Get Device Connection History list
  * @param requestBody
  * @param req
  * @returns
  */
  public getDeviceConnectionHistoryList(requestBody: DeviceConnectionHistorySearchRequestBody, req: any): Observable<HttpResponse<DeviceConnectionHistoryPegableResponse>> {
    const options = createRequestOption(req);
    return this.http.post<DeviceConnectionHistoryPegableResponse>(this.deviceConnectionHistory + "/devices/search", requestBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Get Device Connection History Detail
  * @param deviceId
  * @returns
  */
  public getDeviceConnectionHistoryDetail(deviceId: number): Observable<HttpResponse<DeviceConnectionHistoryDetails>> {
    return this.http.get<DeviceConnectionHistoryDetails>(this.deviceConnectionHistory + "/devices/" + deviceId, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Get Device Probe Connection History list
  * @param deviceId
  * @param requestBody
  * @param req
  * @returns
  */
  public getDeviceProbeConnectionHistoryList(deviceId: number, requestBody: DeviceProbeConnectionHistorySearchRequestBody, req: any): Observable<HttpResponse<DeviceProbeConnectionHistoryPagableResponse>> {
    const options = createRequestOption(req);
    return this.http.post<DeviceProbeConnectionHistoryPagableResponse>(this.deviceConnectionHistory + "/devices/" + deviceId + "/probes/search", requestBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Generate CSV file for Device Connection History
  * @param requestBody - Export CSV search request
  * @returns Observable with file generation response
  */
  public generateCSVFileForDeviceConnectionHistory(requestBody: DeviceConnectionHistoryExportCSVSearchRequest): Observable<HttpResponse<any>> {
    return this.http.post<any>(this.deviceConnectionHistory + '/devices/generateCSV', requestBody, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Download CSV file for Device Connection History
  * @param fileName - Name of the generated CSV file
  * @returns Observable with file download response
  */
  public downloadCSVFileForDeviceConnectionHistory(fileName: string): Observable<HttpResponse<any>> {
    return this.http.get<any>(this.deviceConnectionHistory + '/devices/downloadDeviceConnectionHistoryData/' + fileName, { responseType: 'blob' as 'json' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Generate CSV file for Device Probe Connection History
  * @param requestBody - Export CSV search request
  * @returns Observable with file generation response
  */
  public generateCSVFileForDeviceProbeConnectionHistory(requestBody: DeviceProbeConnectionHistoryExportCSVSearchRequest): Observable<HttpResponse<any>> {
    return this.http.post<any>(this.deviceConnectionHistory + '/devices/probes/generateCSV', requestBody, { observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }

  /**
  * Download CSV file for Device Probe Connection History
  * @param fileName - Name of the generated CSV file
  * @returns Observable with file download response
  */
  public downloadCSVFileForDeviceProbeConnectionHistory(fileName: string): Observable<HttpResponse<any>> {
    return this.http.get<any>(this.deviceConnectionHistory + '/devices/probes/downloadDeviceProbeConnectionHistoryData/' + fileName, { responseType: 'blob' as 'json' }).pipe(catchError(this.commonsService.handleError));
  }
}
