import { ProbeTypeEnum } from "src/app/shared/enum/ProbeType.enum";

export class ProbeConnectionHistoryResponse {
    id: number;
    probeSerialNumber: string;
    probePartNumber: string;
    probeType: ProbeTypeEnum;
    lastConnectedDate: number;

    constructor(id: number, probeSerialNumber: string, probePartNumber: string, probeType: ProbeTypeEnum, lastConnectedDate: number) {
        this.id = id;
        this.probeSerialNumber = probeSerialNumber;
        this.probePartNumber = probePartNumber;
        this.probeType = probeType;
        this.lastConnectedDate = lastConnectedDate;
    }
}