import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProbeConnectionHistoryListComponent } from './probe-connection-history-list.component';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { ProbeConnectionHistoryOperationService } from '../probe-connection-history-services/probe-connection-history-operation/probe-connection-history-operation.service';
import { Subject } from 'rxjs';
import { ProbeConnectionHistoryFilterAction } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistoryFilterAction.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistorySearchRequestBody.model';
import { ProbeConnectionHistoryResponse } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistoryResponse.model';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';

describe('ProbeConnectionHistoryListComponent', () => {
  let component: ProbeConnectionHistoryListComponent;
  let fixture: ComponentFixture<ProbeConnectionHistoryListComponent>;
  let mockAuthService: jasmine.SpyObj<AuthJwtService>;
  let mockCommonsService: jasmine.SpyObj<CommonsService>;
  let mockCommonCheckboxService: jasmine.SpyObj<CommonCheckboxService>;
  let mockPermissionService: jasmine.SpyObj<PermissionService>;
  let mockProbeConnectionHistoryOperationService: jasmine.SpyObj<ProbeConnectionHistoryOperationService>;

  beforeEach(async () => {
    const localStorageServiceMock = jasmine.createSpyObj('LocalStorageService', ['retrieve']);
    const authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    const commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['accessDataSizes']);
    const commonCheckboxServiceSpy = jasmine.createSpyObj('CommonCheckboxService', [
      'defaultSelectAll', 'selectAllItem', 'clearSelectAllCheckbox'
    ]);
    const permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getConnectionHistoryPermission']);
    const probeConnectionHistoryOperationServiceSpy = jasmine.createSpyObj('ProbeConnectionHistoryOperationService', [
      'getProbeConnectionHistoryListFilterRequestParameterSubject',
      'callRefreshPageSubject',
      'loadProbeConnectionHistoryList'
    ]);

    await TestBed.configureTestingModule({
      declarations: [ProbeConnectionHistoryListComponent],
      providers: [
        { provide: LocalStorageService, useValue: localStorageServiceMock },
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: CommonCheckboxService, useValue: commonCheckboxServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: ProbeConnectionHistoryOperationService, useValue: probeConnectionHistoryOperationServiceSpy },
        SessionStorageService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        PrintListPipe,
        commonsProviders(null)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(ProbeConnectionHistoryListComponent);
    component = fixture.componentInstance;

    mockAuthService = TestBed.inject(AuthJwtService) as jasmine.SpyObj<AuthJwtService>;
    mockCommonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
    mockCommonCheckboxService = TestBed.inject(CommonCheckboxService) as jasmine.SpyObj<CommonCheckboxService>;
    mockPermissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
    mockProbeConnectionHistoryOperationService = TestBed.inject(ProbeConnectionHistoryOperationService) as jasmine.SpyObj<ProbeConnectionHistoryOperationService>;

    // Setup default mocks
    mockAuthService.isAuthenticate.and.returnValue(true);
    mockCommonsService.accessDataSizes.and.returnValue(['10', '50', '100']);
    mockPermissionService.getConnectionHistoryPermission.and.returnValue(true);
    mockProbeConnectionHistoryOperationService.getProbeConnectionHistoryListFilterRequestParameterSubject.and.returnValue(new Subject<ProbeConnectionHistoryFilterAction>());
    mockProbeConnectionHistoryOperationService.loadProbeConnectionHistoryList.and.returnValue(Promise.resolve({
      success: true,
      probeConnectionHistoryList: [],
      totalRecordDisplay: 0,
      totalRecord: 0,
      localProbeConnectionHistoryList: [],
      totalItems: 0,
      page: 1
    }));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should navigate to login when not authenticated', () => {
    mockAuthService.isAuthenticate.and.returnValue(false);

    component.ngOnInit();

    expect(mockAuthService.loginNavigate).toHaveBeenCalled();
  });

  it('should initialize component when authenticated', () => {
    mockAuthService.isAuthenticate.and.returnValue(true);
    spyOn(component, 'subjectInit' as any);

    component.ngOnInit();

    expect(component.page).toBe(0);
    expect(component.selectedProbeConnectionHistoryIdList).toEqual([]);
    expect(component.isFilterComponentInitWithApicall).toBe(true);
    expect(component.listPageRefreshForbackToDetailPage).toBe(false);
    expect(component.isFilterHidden).toBe(false);
    expect(component.probeConnectionHistoryListDisplay).toBe(true);
    expect(component.probeConnectionHistoryDetailDisplay).toBe(false);
    expect(mockCommonsService.accessDataSizes).toHaveBeenCalled();
    expect((component as any).subjectInit).toHaveBeenCalled();
  });

  it('should set connection history permission', () => {
    mockPermissionService.getConnectionHistoryPermission.and.returnValue(true);

    (component as any).setConnectionHistoryPermission();

    expect(component.connectioHistoryAdminPermission).toBe(true);
    expect(mockPermissionService.getConnectionHistoryPermission).toHaveBeenCalledWith(PermissionAction.GET_CONNECTION_HISTORY_ACTION);
  });

  it('should initialize subject subscription', () => {
    const mockSubject = new Subject<ProbeConnectionHistoryFilterAction>();
    mockProbeConnectionHistoryOperationService.getProbeConnectionHistoryListFilterRequestParameterSubject.and.returnValue(mockSubject);

    (component as any).subjectInit();

    expect(mockProbeConnectionHistoryOperationService.getProbeConnectionHistoryListFilterRequestParameterSubject).toHaveBeenCalled();
  });

  it('should handle subject subscription with default page number', () => {
    const mockSubject = new Subject<ProbeConnectionHistoryFilterAction>();
    mockProbeConnectionHistoryOperationService.getProbeConnectionHistoryListFilterRequestParameterSubject.and.returnValue(mockSubject);
    spyOn(component, 'resetPage' as any);
    spyOn(component, 'loadAll');

    (component as any).subjectInit();

    const testFilterAction = new ProbeConnectionHistoryFilterAction(
      new ListingPageReloadSubjectParameter(true, true, false, false),
      new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null)
    );
    mockSubject.next(testFilterAction);

    expect(component.selectedProbeConnectionHistoryIdList).toEqual([]);
    expect((component as any).resetPage).toHaveBeenCalled();
    expect(component.loadAll).toHaveBeenCalled();
  });

  it('should handle subject subscription without default page number', () => {
    const mockSubject = new Subject<ProbeConnectionHistoryFilterAction>();
    mockProbeConnectionHistoryOperationService.getProbeConnectionHistoryListFilterRequestParameterSubject.and.returnValue(mockSubject);
    spyOn(component, 'loadAll');

    (component as any).subjectInit();

    const testFilterAction = new ProbeConnectionHistoryFilterAction(
      new ListingPageReloadSubjectParameter(true, false, false, false),
      new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null)
    );
    mockSubject.next(testFilterAction);

    expect(component.loadAll).toHaveBeenCalled();
  });

  it('should unsubscribe on ngOnDestroy', () => {
    const mockSubscription = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    component.subscriptionForProbeConnectionHistoryListFilterRequestParameter = mockSubscription;

    component.ngOnDestroy();

    expect(mockSubscription.unsubscribe).toHaveBeenCalled();
  });

  it('should handle ngOnDestroy when subscription is undefined', () => {
    component.subscriptionForProbeConnectionHistoryListFilterRequestParameter = undefined;

    expect(() => component.ngOnDestroy()).not.toThrow();
  });

  it('should reset page', () => {
    component.page = 5;
    component.previousPage = 3;

    (component as any).resetPage();

    expect(component.page).toBe(0);
    expect(component.previousPage).toBe(1);
  });

  it('should refresh filter with clear filter', async () => {
    spyOn(component, 'resetPage' as any);
    spyOn(component, 'filterPageSubjectCallForReloadPage');

    await component.refreshFilter(true);

    expect(component.loading).toBe(true);
    expect((component as any).resetPage).toHaveBeenCalled();
    expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, true);
  });

  it('should refresh filter without clear filter', async () => {
    spyOn(component, 'resetPage' as any);
    spyOn(component, 'filterPageSubjectCallForReloadPage');

    await component.refreshFilter(false);

    expect(component.loading).toBe(true);
    expect((component as any).resetPage).toHaveBeenCalled();
    expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
  });

  it('should change data size', () => {
    const mockEvent = { target: { value: '50' } };
    spyOn(component, 'setLoadingStatus' as any);
    spyOn(component, 'filterPageSubjectCallForReloadPage');

    component.changeDataSize(mockEvent);

    expect((component as any).setLoadingStatus).toHaveBeenCalledWith(true);
    expect(component.selectedProbeConnectionHistoryIdList).toEqual([]);
    expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
  });

  it('should select checkbox when checked', () => {
    const mockProbeConnectionHistory = { id: 1 } as ProbeConnectionHistoryResponse;
    spyOn(component, 'defaultSelectAll' as any);

    component.selectCheckbox(mockProbeConnectionHistory, true);

    expect(component.selectedProbeConnectionHistoryIdList).toContain(1);
    expect((component as any).defaultSelectAll).toHaveBeenCalled();
  });

  it('should deselect checkbox when unchecked', () => {
    const mockProbeConnectionHistory = { id: 1 } as ProbeConnectionHistoryResponse;
    component.selectedProbeConnectionHistoryIdList = [1, 2, 3];
    spyOn(component, 'defaultSelectAll' as any);

    component.selectCheckbox(mockProbeConnectionHistory, false);

    expect(component.selectedProbeConnectionHistoryIdList).not.toContain(1);
    expect((component as any).defaultSelectAll).toHaveBeenCalled();
  });

  it('should call defaultSelectAll', () => {
    component.localProbeConnectionHistoryIdListArray = [1, 2, 3];
    component.selectedProbeConnectionHistoryIdList = [1, 2];
    component.selectAllCheckboxId = 'testId';

    (component as any).defaultSelectAll();

    expect(mockCommonCheckboxService.defaultSelectAll).toHaveBeenCalledWith([1, 2, 3], [1, 2], 'testId');
  });

  it('should select all items', () => {
    component.localProbeConnectionHistoryIdListArray = [1, 2, 3];
    component.selectedProbeConnectionHistoryIdList = [];
    component.checkboxListName = 'testName';
    mockCommonCheckboxService.selectAllItem.and.returnValue([1, 2, 3]);

    component.selectAllItem(true);

    expect(mockCommonCheckboxService.selectAllItem).toHaveBeenCalledWith(true, [1, 2, 3], [], 'testName');
    expect(component.selectedProbeConnectionHistoryIdList).toEqual([1, 2, 3]);
  });

  it('should load page when page is different', () => {
    component.previousPage = 1;
    component.selectAllCheckboxId = 'testId';
    spyOn(component, 'filterPageSubjectCallForReloadPage');

    component.loadPage(2);

    expect(component.previousPage).toBe(2);
    expect(mockCommonCheckboxService.clearSelectAllCheckbox).toHaveBeenCalledWith('testId');
    expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(false, false);
  });

  it('should not load page when page is same', () => {
    component.previousPage = 2;
    spyOn(component, 'filterPageSubjectCallForReloadPage');

    component.loadPage(2);

    expect(component.filterPageSubjectCallForReloadPage).not.toHaveBeenCalled();
  });

  it('should call refresh page subject', () => {
    component.isFilterHidden = false;

    component.filterPageSubjectCallForReloadPage(true, false);

    expect(mockProbeConnectionHistoryOperationService.callRefreshPageSubject).toHaveBeenCalled();
  });

  it('should toggle filter to hidden', () => {
    component.isFilterHidden = false;

    component.toggleFilter();

    expect(component.isFilterComponentInitWithApicall).toBe(false);
    expect(component.listPageRefreshForbackToDetailPage).toBe(false);
    expect(component.isFilterHidden).toBe(true);
    expect(component.hideShowFilterButtonText).toBe('Show Filter');
  });

  it('should toggle filter to visible', () => {
    component.isFilterHidden = true;

    component.toggleFilter();

    expect(component.isFilterComponentInitWithApicall).toBe(false);
    expect(component.listPageRefreshForbackToDetailPage).toBe(false);
    expect(component.isFilterHidden).toBe(false);
    expect(component.hideShowFilterButtonText).toBe('Hide Filter');
  });

  it('should load all data successfully', async () => {
    const mockSearchRequestBody = new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null);
    const mockProbeConnectionHistoryList = [
      { id: 1, probeSerialNumber: 'SN001', probePartNumber: 'PN001', probeType: ProbeTypeEnum.TORSO1, lastConnectedDate: new Date().getTime() } as ProbeConnectionHistoryResponse,
      { id: 2, probeSerialNumber: 'SN002', probePartNumber: 'PN002', probeType: ProbeTypeEnum.TORSO3, lastConnectedDate: new Date().getTime() } as ProbeConnectionHistoryResponse
    ];
    const mockResult = {
      success: true,
      probeConnectionHistoryList: mockProbeConnectionHistoryList,
      totalRecordDisplay: 2,
      totalRecord: 10,
      localProbeConnectionHistoryList: mockProbeConnectionHistoryList,
      totalItems: 10,
      page: 1
    };
    mockProbeConnectionHistoryOperationService.loadProbeConnectionHistoryList.and.returnValue(Promise.resolve(mockResult));
    spyOn(component, 'setLoadingStatus' as any);
    spyOn(component, 'setLocalProbeConnectionHistoryId');
    spyOn(component, 'defaultSelectAll' as any);

    await component.loadAll(mockSearchRequestBody);

    expect(component.ProbeConnectionHistorySearchRequestBody).toBe(mockSearchRequestBody);
    expect(component.probeConnectionHistoryResponseList).toEqual(mockProbeConnectionHistoryList);
    expect(component.totalRecordDisplay).toBe(2);
    expect(component.totalRecord).toBe(10);
    expect(component.totalItems).toBe(10);
    expect(component.page).toBe(1);
    expect(component.setLocalProbeConnectionHistoryId).toHaveBeenCalledWith(mockProbeConnectionHistoryList);
    expect((component as any).setLoadingStatus).toHaveBeenCalledWith(false);
    expect((component as any).defaultSelectAll).toHaveBeenCalled();
  });

  it('should handle load all data failure', async () => {
    const mockSearchRequestBody = new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null);
    const mockResult = {
      success: false,
      probeConnectionHistoryList: [],
      totalRecordDisplay: 0,
      totalRecord: 0,
      localProbeConnectionHistoryList: [],
      totalItems: 0,
      page: 0
    };
    mockProbeConnectionHistoryOperationService.loadProbeConnectionHistoryList.and.returnValue(Promise.resolve(mockResult));
    spyOn(component, 'setLoadingStatus' as any);
    spyOn(component, 'defaultSelectAll' as any);

    await component.loadAll(mockSearchRequestBody);

    expect(component.probeConnectionHistoryResponseList).toEqual([]);
    expect(component.totalRecordDisplay).toBe(0);
    expect(component.totalRecord).toBe(0);
    expect(component.totalItems).toBe(0);
    expect((component as any).setLoadingStatus).toHaveBeenCalledWith(false);
    expect((component as any).defaultSelectAll).toHaveBeenCalled();
  });

  it('should set local probe connection history id', () => {
    const mockList = [
      { id: 1, probeSerialNumber: 'SN001', probePartNumber: 'PN001', probeType: ProbeTypeEnum.TORSO1, lastConnectedDate: new Date().getTime() },
      { id: 2, probeSerialNumber: 'SN002', probePartNumber: 'PN002', probeType: ProbeTypeEnum.TORSO3, lastConnectedDate: new Date().getTime() },
      { id: 3, probeSerialNumber: 'SN003', probePartNumber: 'PN003', probeType: ProbeTypeEnum.LEXSA, lastConnectedDate: new Date().getTime() }
    ] as ProbeConnectionHistoryResponse[];
    spyOn(component, 'defaultSelectAll' as any);

    component.setLocalProbeConnectionHistoryId(mockList);

    expect(component.localProbeConnectionHistoryIdListArray).toEqual([1, 2, 3]);
    expect((component as any).defaultSelectAll).toHaveBeenCalled();
  });

  it('should set loading status', () => {
    (component as any).setLoadingStatus(true);
    expect(component.loading).toBe(true);

    (component as any).setLoadingStatus(false);
    expect(component.loading).toBe(false);
  });

  it('should show probe connection history list', () => {
    component.isFilterHidden = true;
    spyOn(component, 'filterPageSubjectCallForReloadPage');

    component.showProbeConnectionHistoryList();

    expect(component.isFilterComponentInitWithApicall).toBe(false);
    expect(component.listPageRefreshForbackToDetailPage).toBe(true);
    expect(component.probeConnectionHistoryId).toBeNull();
    expect(component.probeConnectionHistoryListDisplay).toBe(true);
    expect(component.probeConnectionHistoryDetailDisplay).toBe(false);
    expect(component.selectedProbeConnectionHistoryIdList).toEqual([]);
    expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
  });

  it('should show probe connection history list without calling filter when filter is visible', () => {
    component.isFilterHidden = false;
    spyOn(component, 'filterPageSubjectCallForReloadPage');

    component.showProbeConnectionHistoryList();

    expect(component.filterPageSubjectCallForReloadPage).not.toHaveBeenCalled();
  });

  it('should show probe connection history detail', () => {
    component.showProbeConnectionHistoryDetail(123);

    expect(component.probeConnectionHistoryId).toBe(123);
    expect(component.probeConnectionHistoryListDisplay).toBe(false);
    expect(component.probeConnectionHistoryDetailDisplay).toBe(true);
  });

  it('should emit show device connection list display', () => {
    spyOn(component.showDeviceConnectionHistoryListDisplay, 'emit');

    component.showDeviceConnectionListDisplay();

    expect(component.showDeviceConnectionHistoryListDisplay.emit).toHaveBeenCalled();
  });

  it('should click on refresh button', async () => {
    spyOn(component, 'resetPage' as any);
    spyOn(component, 'filterPageSubjectCallForReloadPage');

    await component.clickOnRefreshButton();

    expect(component.loading).toBe(true);
    expect((component as any).resetPage).toHaveBeenCalled();
    expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalledWith(true, false);
  });
});
