import { TestBed } from '@angular/core/testing';
import { ProbeConnectionHistoryOperationService } from './probe-connection-history-operation.service';
import { ProbeConnectionHistoryApiCallService } from '../probe-connection-history-api-call/probe-connection-history-api-call.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ToastrService } from 'ngx-toastr';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ProbeConnectionHistoryFilterAction } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistoryFilterAction.model';
import { ProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistorySearchRequestBody.model';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { HttpResponse } from '@angular/common/http';
import { ProbeConnectionHistoryPegableResponse } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistoryPegableResponse.model';
import { of, throwError } from 'rxjs';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';

describe('ProbeConnectionHistoryOperationService', () => {
  let service: ProbeConnectionHistoryOperationService;
  let mockProbeConnectionApiCallService: jasmine.SpyObj<ProbeConnectionHistoryApiCallService>;
  let mockExceptionHandlingService: jasmine.SpyObj<ExceptionHandlingService>;
  let mockToastrService: jasmine.SpyObj<ToastrService>;
  let mockPermissionService: jasmine.SpyObj<PermissionService>;
  let mockCommonOperationService: jasmine.SpyObj<CommonOperationsService>;
  let mockCommonsService: jasmine.SpyObj<CommonsService>;

  beforeEach(() => {
    const probeConnectionApiCallServiceSpy = jasmine.createSpyObj('ProbeConnectionHistoryApiCallService', [
      'getProbeConnectionHistoryList'
    ]);
    const exceptionHandlingServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', [
      'customErrorMessage'
    ]);
    const toastrServiceSpy = jasmine.createSpyObj('ToastrService', ['error', 'info']);
    const permissionServiceSpy = jasmine.createSpyObj('PermissionService', [
      'getConnectionHistoryPermission'
    ]);
    const commonOperationServiceSpy = jasmine.createSpyObj('CommonOperationsService', [
      'dateValidation', 'getFilterValue'
    ]);
    const commonsServiceSpy = jasmine.createSpyObj('CommonsService', [
      'checkValueIsNullOrEmpty', 'getEndTimeOfDay'
    ]);

    TestBed.configureTestingModule({
      providers: [
        ProbeConnectionHistoryOperationService,
        { provide: ProbeConnectionHistoryApiCallService, useValue: probeConnectionApiCallServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionHandlingServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: CommonOperationsService, useValue: commonOperationServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy }
      ]
    });

    service = TestBed.inject(ProbeConnectionHistoryOperationService);
    mockProbeConnectionApiCallService = TestBed.inject(ProbeConnectionHistoryApiCallService) as jasmine.SpyObj<ProbeConnectionHistoryApiCallService>;
    mockExceptionHandlingService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    mockToastrService = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    mockPermissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
    mockCommonOperationService = TestBed.inject(CommonOperationsService) as jasmine.SpyObj<CommonOperationsService>;
    mockCommonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;

    // Setup default mocks
    mockPermissionService.getConnectionHistoryPermission.and.returnValue(true);
    mockCommonOperationService.dateValidation.and.returnValue(true);
    mockCommonOperationService.getFilterValue.and.returnValue('test');
    mockCommonsService.checkValueIsNullOrEmpty.and.returnValue(false);
    mockCommonsService.getEndTimeOfDay.and.returnValue(new Date());
  });

  // Test 1: Service creation and subject getters (covers constructor and getter methods)
  it('should be created and provide subjects', () => {
    expect(service).toBeTruthy();
    expect(service.getProbeConnectionHistoryListRefreshSubject()).toBeDefined();
    expect(service.getProbeConnectionHistoryDetailRefreshSubject()).toBeDefined();
    expect(service.getProbeConnectionHistoryListFilterRequestParameterSubject()).toBeDefined();
  });

  // Test 2: Subject communication methods (covers callProbeConnectionHistoryListFilterRequestParameterSubject)
  it('should call filter request parameter subject', () => {
    const testFilterAction = new ProbeConnectionHistoryFilterAction(
      new ListingPageReloadSubjectParameter(true, true, false, false),
      new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null)
    );
    spyOn(service.getProbeConnectionHistoryListFilterRequestParameterSubject(), 'next');

    service.callProbeConnectionHistoryListFilterRequestParameterSubject(testFilterAction);

    expect(service.getProbeConnectionHistoryListFilterRequestParameterSubject().next).toHaveBeenCalledWith(testFilterAction);
  });

  // Test 3: Refresh page subject with all resource types and filter states (covers callRefreshPageSubject method)
  it('should handle refresh page subject for different resources and filter states', () => {
    const testParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    spyOn(service, 'callProbeConnectionHistoryListFilterRequestParameterSubject');
    spyOn(service.getProbeConnectionHistoryListRefreshSubject(), 'next');
    spyOn(service.getProbeConnectionHistoryDetailRefreshSubject(), 'next');

    // Test ProbeConnectionHistoryListResource with filter hidden
    service.callRefreshPageSubject(testParameter, 'probeConnectionHistoryListResource', true);
    expect(service.callProbeConnectionHistoryListFilterRequestParameterSubject).toHaveBeenCalled();

    // Test ProbeConnectionHistoryListResource with filter visible
    service.callRefreshPageSubject(testParameter, 'probeConnectionHistoryListResource', false);
    expect(service.getProbeConnectionHistoryListRefreshSubject().next).toHaveBeenCalledWith(testParameter);

    // Test ProbeConnectionHistoryDetailResource
    service.callRefreshPageSubject(testParameter, 'probeConnectionHistoryDetailResource', false);
    expect(service.getProbeConnectionHistoryDetailRefreshSubject().next).toHaveBeenCalledWith(testParameter);
  });

  // Test 4: Filter page subject call (covers filterPageSubjectCallForReloadPage)
  it('should call filter page subject for reload page', () => {
    spyOn(service, 'callRefreshPageSubject');

    service.filterPageSubjectCallForReloadPage(true, false);

    expect(service.callRefreshPageSubject).toHaveBeenCalledWith(
      jasmine.any(ListingPageReloadSubjectParameter),
      'probeConnectionHistoryListResource',
      false
    );
  });

  // Test 5: Load data success scenario (covers loadProbeConnectionHistoryList success path)
  it('should load probe connection history list successfully', async () => {
    const mockSearchRequestBody = new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };
    const mockResponse = new HttpResponse({
      status: 200,
      body: { content: [], numberOfElements: 0, totalElements: 0, number: 0 } as ProbeConnectionHistoryPegableResponse
    });

    mockProbeConnectionApiCallService.getProbeConnectionHistoryList.and.returnValue(of(mockResponse));

    const result = await service.loadProbeConnectionHistoryList(mockSearchRequestBody, mockPageObj);

    expect(result.success).toBe(true);
    expect(result.page).toBe(1);
    expect(mockPermissionService.getConnectionHistoryPermission).toHaveBeenCalledWith(PermissionAction.GET_CONNECTION_HISTORY_ACTION);
  });

  // Test 6: Load data error scenarios (covers permission failure, non-200 status, and exception handling)
  it('should handle load data error scenarios', async () => {
    const mockSearchRequestBody = new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };

    // Test insufficient permissions
    mockPermissionService.getConnectionHistoryPermission.and.returnValue(false);
    let result = await service.loadProbeConnectionHistoryList(mockSearchRequestBody, mockPageObj);
    expect(result.success).toBe(false);
    expect(mockToastrService.error).toHaveBeenCalledWith('Insufficient permissions to load Probe connection history list');

    // Reset permission for next tests
    mockPermissionService.getConnectionHistoryPermission.and.returnValue(true);

    // Test non-200 status
    mockProbeConnectionApiCallService.getProbeConnectionHistoryList.and.returnValue(of(new HttpResponse({ status: 404, body: null })));
    result = await service.loadProbeConnectionHistoryList(mockSearchRequestBody, mockPageObj);
    expect(result.success).toBe(false);

    // Test exception handling
    mockProbeConnectionApiCallService.getProbeConnectionHistoryList.and.returnValue(throwError(() => new Error('API Error')));
    result = await service.loadProbeConnectionHistoryList(mockSearchRequestBody, mockPageObj);
    expect(result.success).toBe(false);
    expect(mockExceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // Test 7: Process filter search (covers processFilterSearch with all scenarios)
  it('should process filter search with different scenarios', () => {
    const mockFormValue = { serialNumber: 'SN001', partNumber: 'PN001', probeType: ['Type1'], fromLastConnectedDate: new Date(), toLastConnectedDate: new Date() };
    const mockDefaultParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    spyOn(service, 'validateProbeConnectionHistoryFilterForm').and.returnValue(true);
    spyOn(service, 'buildProbeConnectionHistoryFilterRequestBody').and.returnValue(new ProbeConnectionHistorySearchRequestBody(null, null, null, null, null));
    spyOn(service, 'callProbeConnectionHistoryListFilterRequestParameterSubject');

    // Test successful processing
    let result = service.processFilterSearch(mockFormValue, false, mockDefaultParameter);
    expect(result).toBe(true);
    expect(service.callProbeConnectionHistoryListFilterRequestParameterSubject).toHaveBeenCalled();

    // Test invalid form
    result = service.processFilterSearch({}, true, mockDefaultParameter);
    expect(result).toBe(false);

    // Test validation failure
    (service.validateProbeConnectionHistoryFilterForm as jasmine.Spy).and.returnValue(false);
    result = service.processFilterSearch({}, false, mockDefaultParameter);
    expect(result).toBe(false);
  });

  // Test 8: Validate filter form (covers validateProbeConnectionHistoryFilterForm with all scenarios)
  it('should validate probe connection history filter form', () => {
    // Test successful validation
    const validFormValue = { serialNumber: 'SN001', partNumber: 'PN001', probeType: ['Type1'], fromLastConnectedDate: new Date(), toLastConnectedDate: new Date() };
    let result = service.validateProbeConnectionHistoryFilterForm(validFormValue);
    expect(result).toBe(true);
    expect(mockCommonOperationService.dateValidation).toHaveBeenCalled();

    // Test no filters selected
    const emptyFormValue = { serialNumber: null, partNumber: null, probeType: null, fromLastConnectedDate: null, toLastConnectedDate: null };
    result = service.validateProbeConnectionHistoryFilterForm(emptyFormValue);
    expect(result).toBe(false);
    expect(mockToastrService.info).toHaveBeenCalledWith('Please Select Filter To Search');

    // Test date validation failure
    mockCommonOperationService.dateValidation.and.returnValue(false);
    result = service.validateProbeConnectionHistoryFilterForm(validFormValue);
    expect(result).toBe(false);
  });

  // Test 9: Build filter request body (covers buildProbeConnectionHistoryFilterRequestBody with all scenarios)
  it('should build probe connection history filter request body', () => {
    const mockFormValue = { serialNumber: 'SN001', partNumber: 'PN001', probeType: [ProbeTypeEnum.TORSO1], fromLastConnectedDate: new Date(), toLastConnectedDate: new Date() };
    mockCommonsService.checkValueIsNullOrEmpty.and.returnValue(false);
    mockCommonsService.getEndTimeOfDay.and.returnValue(new Date());
    mockCommonOperationService.getFilterValue.and.returnValue('test');

    let result = service.buildProbeConnectionHistoryFilterRequestBody(mockFormValue);
    expect(result).toBeInstanceOf(ProbeConnectionHistorySearchRequestBody);

    // Test with null values
    const nullFormValue = { serialNumber: null, partNumber: null, probeType: null, fromLastConnectedDate: null, toLastConnectedDate: null };
    mockCommonsService.checkValueIsNullOrEmpty.and.returnValue(true);
    mockCommonOperationService.getFilterValue.and.returnValue(null);
    result = service.buildProbeConnectionHistoryFilterRequestBody(nullFormValue);
    expect(result.probeType).toBeUndefined();

    // Test with empty probeType array
    const emptyProbeTypeFormValue = { serialNumber: 'SN001', partNumber: 'PN001', probeType: [], fromLastConnectedDate: null, toLastConnectedDate: null };
    result = service.buildProbeConnectionHistoryFilterRequestBody(emptyProbeTypeFormValue);
    expect(result.probeType).toBeUndefined();
  });

  // Test 10: Clear filters and cache management (covers clearAllFiltersAndRefresh and cache methods)
  it('should clear filters and manage cache', () => {
    const testParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
    spyOn(service, 'callProbeConnectionHistoryListFilterRequestParameterSubject');

    // Test clear all filters
    service.clearAllFiltersAndRefresh(testParameter);
    expect(service.callProbeConnectionHistoryListFilterRequestParameterSubject).toHaveBeenCalled();

    // Test cache management
    const mockSearchRequest = new ProbeConnectionHistorySearchRequestBody('part', ProbeTypeEnum.TORSO1, 'serial', null, null);
    service.setLastAppliedProbeConnectionHistorySearchRequest(mockSearchRequest);
    expect(service.getLastAppliedProbeConnectionHistorySearchRequest()).toBe(mockSearchRequest);

    // Test null cache
    service.setLastAppliedProbeConnectionHistorySearchRequest(null);
    expect(service.getLastAppliedProbeConnectionHistorySearchRequest()).toBeNull();
  });

  // Test 11: Cached filter data usage in callRefreshPageSubject (covers cached data scenario)
  it('should use cached filter data when available', () => {
    const testParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const cachedSearchRequest = new ProbeConnectionHistorySearchRequestBody('part', ProbeTypeEnum.TORSO1, 'serial', null, null);
    service.setLastAppliedProbeConnectionHistorySearchRequest(cachedSearchRequest);
    spyOn(service, 'callProbeConnectionHistoryListFilterRequestParameterSubject');

    service.callRefreshPageSubject(testParameter, 'probeConnectionHistoryListResource', true);

    expect(service.callProbeConnectionHistoryListFilterRequestParameterSubject).toHaveBeenCalled();
  });
});
