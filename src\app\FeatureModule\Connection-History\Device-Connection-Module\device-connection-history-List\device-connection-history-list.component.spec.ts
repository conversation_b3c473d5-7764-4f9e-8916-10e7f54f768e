import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { Subject } from 'rxjs';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { DeviceConnectionHistoryFilterAction } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryFilterAction.model';
import { DeviceConnectionHistoryResponse } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryResponse.model';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistorySearchRequestBody.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { ConnectionTypeEnum } from 'src/app/shared/enum/ConnectionHistory/ConnectionTypeEnum.enum';
import { OSTypeEnum } from 'src/app/shared/enum/Probe/OSTypeEnum.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DeviceConnectionHistoryOperationService } from '../Device-Connection-History-Service/Device-Connection-History-Operation/device-connection-history-operation-service.service';
import { DeviceConnectionHistoryListComponent } from './device-connection-history-list.component';

describe('DeviceConnectionHistoryListComponent', () => {
  let component: DeviceConnectionHistoryListComponent;
  let fixture: ComponentFixture<DeviceConnectionHistoryListComponent>;
  let authServiceSpy: jasmine.SpyObj<AuthJwtService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  let commonCheckboxServiceSpy: jasmine.SpyObj<CommonCheckboxService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let deviceConnectionHistoryOperationServiceSpy: jasmine.SpyObj<DeviceConnectionHistoryOperationService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;

  const mockDeviceConnectionHistoryResponse: DeviceConnectionHistoryResponse = {
    id: 1,
    deviceSerialNumber: 'TEST123',
    deviceModel: 'TestModel',
    manufacturer: 'TestManufacturer',
    osType: OSTypeEnum.BRIDGE,
    connectionType: ConnectionTypeEnum.INTERNAL,
    lastConnectedDate: 1672531200000
  };

  const mockSearchRequestBody = new DeviceConnectionHistorySearchRequestBody(
    'TestManufacturer',
    'TestModel',
    'TEST123',
    OSTypeEnum.BRIDGE,
    1672531200000
  );

  beforeEach(async () => {
    authServiceSpy = jasmine.createSpyObj('AuthJwtService', ['isAuthenticate', 'loginNavigate']);
    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['accessDataSizes']);
    commonCheckboxServiceSpy = jasmine.createSpyObj('CommonCheckboxService', [
      'defaultSelectAll', 'selectAllItem', 'clearSelectAllCheckbox'
    ]);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getConnectionHistoryPermission']);
    deviceConnectionHistoryOperationServiceSpy = jasmine.createSpyObj('DeviceConnectionHistoryOperationService', [
      'getDeviceConnectionHistoryListFilterRequestParameterSubject',
      'callRefreshPageSubject',
      'loadDeviceConnectionHistoryList'
    ]);
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);

    authServiceSpy.isAuthenticate.and.returnValue(true);
    commonsServiceSpy.accessDataSizes.and.returnValue(['10', '25', '50', '100']);
    permissionServiceSpy.getConnectionHistoryPermission.and.returnValue(true);

    const mockFilterSubject = new Subject<DeviceConnectionHistoryFilterAction>();
    deviceConnectionHistoryOperationServiceSpy.getDeviceConnectionHistoryListFilterRequestParameterSubject.and.returnValue(mockFilterSubject);

    await TestBed.configureTestingModule({
      declarations: [DeviceConnectionHistoryListComponent],
      providers: [
        { provide: AuthJwtService, useValue: authServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: CommonCheckboxService, useValue: commonCheckboxServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        { provide: DeviceConnectionHistoryOperationService, useValue: deviceConnectionHistoryOperationServiceSpy },
        LocalStorageService,
        SessionStorageService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        PrintListPipe,
        commonsProviders(toastrServiceMock)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DeviceConnectionHistoryListComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should redirect to login when not authenticated', () => {
    authServiceSpy.isAuthenticate.and.returnValue(false);
    component.ngOnInit();
    expect(authServiceSpy.loginNavigate).toHaveBeenCalled();
  });

  it('should initialize when authenticated', () => {
    authServiceSpy.isAuthenticate.and.returnValue(true);
    component.ngOnInit();
    expect(component.page).toBe(0);
    expect(component.previousPage).toBe(1);
  });

  it('should handle filter subscription', () => {
    authServiceSpy.isAuthenticate.and.returnValue(true);
    spyOn(component, 'loadAll');
    component.ngOnInit();

    const filterSubject = deviceConnectionHistoryOperationServiceSpy.getDeviceConnectionHistoryListFilterRequestParameterSubject();
    const mockFilterAction = new DeviceConnectionHistoryFilterAction(
      new ListingPageReloadSubjectParameter(true, true, false, false),
      mockSearchRequestBody
    );
    filterSubject.next(mockFilterAction);

    expect(component.loadAll).toHaveBeenCalledWith(mockSearchRequestBody);
  });

  it('should unsubscribe on destroy', () => {
    authServiceSpy.isAuthenticate.and.returnValue(true);
    component.ngOnInit();

    const subscriptionSpy = jasmine.createSpyObj('Subscription', ['unsubscribe']);
    component['subscriptionForDeviceConnectionHistoryListFilterRequestParameter'] = subscriptionSpy;

    component.ngOnDestroy();
    expect(subscriptionSpy.unsubscribe).toHaveBeenCalled();
  });

  it('should load data successfully', async () => {
    const mockResult = {
      success: true,
      deviceConnectionHistoryList: [mockDeviceConnectionHistoryResponse],
      totalRecordDisplay: 1,
      totalRecord: 10,
      totalItems: 10,
      page: 1,
      localDeviceConnectionHistoryList: [mockDeviceConnectionHistoryResponse]
    };

    deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryList.and.returnValue(Promise.resolve(mockResult));
    spyOn(component, 'setLocalDeviceConnectionHistoryId');

    await component.loadAll(mockSearchRequestBody);

    expect(component.deviceConnectionHistoryResponseList).toEqual([mockDeviceConnectionHistoryResponse]);
    expect(component.totalRecordDisplay).toBe(1);
  });

  it('should handle checkbox selection', () => {
    spyOn(component as any, 'defaultSelectAll');

    component.selectCheckbox(mockDeviceConnectionHistoryResponse, true);

    expect(component.selectedDeviceConnectionHistoryIdList).toContain(1);
    expect((component as any).defaultSelectAll).toHaveBeenCalled();
  });

  it('should change data size', () => {
    spyOn(component, 'filterPageSubjectCallForReloadPage');
    const mockEvent = { target: { value: '50' } };

    component.changeDataSize(mockEvent);

    expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalled();
  });

  it('should load page', () => {
    spyOn(component, 'filterPageSubjectCallForReloadPage');
    component.previousPage = 1;

    component.loadPage(2);

    expect(component.previousPage).toBe(2);
    expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalled();
  });

  it('should show device connection history list', () => {
    spyOn(component, 'filterPageSubjectCallForReloadPage');
    component.isFilterHidden = true;

    component.showDeviceConnectionHistoryList();

    expect(component.deviceConnectionHistoryListDisplay).toBe(true);
    expect(component.deviceConnectionHistoryDetailDisplay).toBe(false);
  });

  it('should show device connection history detail', () => {
    component.showDeviceConnectionHistoryDetail(123);

    expect(component.deviceConnectionHistoryId).toBe(123);
    expect(component.deviceConnectionHistoryListDisplay).toBe(false);
    expect(component.deviceConnectionHistoryDetailDisplay).toBe(true);
  });

  it('should show probe connection list display', () => {
    spyOn(component.showProbeConnectionHistoryListDisplay, 'emit');

    component.showProbeConnectionListDisplay();

    expect(component.showProbeConnectionHistoryListDisplay.emit).toHaveBeenCalled();
  });

  it('should refresh filter', async () => {
    spyOn(component, 'filterPageSubjectCallForReloadPage');

    await component.refreshFilter(true);

    expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalled();
  });

  it('should click refresh button', async () => {
    spyOn(component, 'filterPageSubjectCallForReloadPage');

    await component.clickOnRefreshButton();

    expect(component.filterPageSubjectCallForReloadPage).toHaveBeenCalled();
  });

  it('should toggle filter', () => {
    const initialState = component.isFilterHidden;

    component.toggleFilter();

    expect(component.isFilterHidden).toBe(!initialState);
  });

  it('should select all items', () => {
    commonCheckboxServiceSpy.selectAllItem.and.returnValue([1, 2]);

    component.selectAllItem(true);

    expect(component.selectedDeviceConnectionHistoryIdList).toEqual([1, 2]);
  });

  it('should set local device connection history id', () => {
    spyOn(component as any, 'defaultSelectAll');

    component.setLocalDeviceConnectionHistoryId([mockDeviceConnectionHistoryResponse]);

    expect(component.localDeviceConnectionHistoryIdListArray).toEqual([1]);
  });
});
