import { TestBed } from '@angular/core/testing';
import { DeviceConnectionHistoryOperationService } from './device-connection-history-operation-service.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { DeviceConnectionApiCallService } from '../Device-connection-Api-Call/device-connection-api-call.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ToastrService } from 'ngx-toastr';
import { PermissionService } from 'src/app/shared/permission.service';
import { of, throwError } from 'rxjs';
import { HttpResponse, HttpErrorResponse } from '@angular/common/http';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistorySearchRequestBody.model';
import { DeviceConnectionHistoryFilterAction } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryFilterAction.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { DeviceConnectionHistoryPegableResponse } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryPegableResponse.mode';
import { DeviceConnectionHistoryResponse } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryResponse.model';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { DeviceConnectionHistoryDetailResource, DeviceConnectionHistoryListResource, COMMON_SELECT_FILTER } from 'src/app/app.constants';
import { OSTypeEnum } from 'src/app/shared/enum/Probe/OSTypeEnum.enum';
import { ConnectionTypeEnum } from 'src/app/shared/enum/ConnectionHistory/ConnectionTypeEnum.enum';
import { Pageable } from 'src/app/model/common/pageable.model';
import { Sort } from 'src/app/model/common/sort.model';
import { ConfirmDialogService } from 'src/app/FeatureModule/CommonComponent/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';

describe('DeviceConnectionHistoryOperationService', () => {
  let service: DeviceConnectionHistoryOperationService;
  let deviceConnectionApiCallServiceSpy: jasmine.SpyObj<DeviceConnectionApiCallService>;
  let exceptionHandlingServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;

  const mockDeviceConnectionHistoryResponse: DeviceConnectionHistoryResponse = {
    id: 1,
    deviceSerialNumber: 'TEST123',
    deviceModel: 'TestModel',
    manufacturer: 'TestManufacturer',
    osType: OSTypeEnum.BRIDGE,
    connectionType: ConnectionTypeEnum.INTERNAL,
    lastConnectedDate: 1672531200000
  };

  const mockSort = new Sort(false, true, false);
  const mockPageable = new Pageable(mockSort, 0, 10, 0, true, false);
  const mockPagableResponse: DeviceConnectionHistoryPegableResponse = new DeviceConnectionHistoryPegableResponse(
    mockPageable,
    1,
    false,
    10,
    2,
    true,
    mockSort,
    10,
    0,
    false,
    [mockDeviceConnectionHistoryResponse]
  );

  const mockSearchRequestBody = new DeviceConnectionHistorySearchRequestBody(
    'TestManufacturer',
    'TestModel',
    'TEST123',
    null,
    1672531200000
  );

  beforeEach(() => {
    deviceConnectionApiCallServiceSpy = jasmine.createSpyObj('DeviceConnectionApiCallService', [
      'getDeviceConnectionHistoryList'
    ]);
    exceptionHandlingServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', [
      'customErrorMessage'
    ]);
    toastrServiceSpy = jasmine.createSpyObj('ToastrService', [
      'success', 'error', 'warning', 'info'
    ]);
    permissionServiceSpy = jasmine.createSpyObj('PermissionService', [
      'getConnectionHistoryPermission'
    ]);

    permissionServiceSpy.getConnectionHistoryPermission.and.returnValue(true);

    TestBed.configureTestingModule({
      providers: [
        DeviceConnectionHistoryOperationService,
        { provide: DeviceConnectionApiCallService, useValue: deviceConnectionApiCallServiceSpy },
        CommonsService,
        { provide: ExceptionHandlingService, useValue: exceptionHandlingServiceSpy },
        { provide: ToastrService, useValue: toastrServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        LocalStorageService,
        SessionStorageService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        commonsProviders(toastrServiceSpy)
      ]
    });
    service = TestBed.inject(DeviceConnectionHistoryOperationService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return device connection history list refresh subject', () => {
    const subject = service.getDeviceConnectionHistoryListRefreshSubject();
    expect(subject).toBeDefined();
  });

  it('should return device connection history detail refresh subject', () => {
    const subject = service.getDeviceConnectionHistoryDetailRefreshSubject();
    expect(subject).toBeDefined();
  });

  it('should return device connection history list filter request parameter subject', () => {
    const subject = service.getDeviceConnectionHistoryListFilterRequestParameterSubject();
    expect(subject).toBeDefined();
  });

  it('should call device connection history list filter request parameter subject', () => {
    const mockFilterAction = new DeviceConnectionHistoryFilterAction(
      new ListingPageReloadSubjectParameter(true, true, false, false),
      mockSearchRequestBody
    );

    spyOn(service.getDeviceConnectionHistoryListFilterRequestParameterSubject(), 'next');

    service.callDeviceConnectionHistoryListFilterRequestParameterSubject(mockFilterAction);

    expect(service.getDeviceConnectionHistoryListFilterRequestParameterSubject().next).toHaveBeenCalledWith(mockFilterAction);
  });

  it('should call refresh page subject', () => {
    const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    spyOn(service, 'callDeviceConnectionHistoryListFilterRequestParameterSubject');

    service.callRefreshPageSubject(mockParameter, DeviceConnectionHistoryListResource, true);

    expect(service.callDeviceConnectionHistoryListFilterRequestParameterSubject).toHaveBeenCalled();
  });

  it('should use cached search request when filter hidden and not clearing', () => {
    const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    service.setLastAppliedDeviceConnectionHistorySearchRequest(mockSearchRequestBody);
    spyOn(service.getDeviceConnectionHistoryListFilterRequestParameterSubject(), 'next');

    service.callRefreshPageSubject(mockParameter, DeviceConnectionHistoryListResource, true);

    expect(service.getDeviceConnectionHistoryListFilterRequestParameterSubject().next).toHaveBeenCalled();
  });

  it('should emit list refresh when filter is visible', () => {
    const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    spyOn(service.getDeviceConnectionHistoryListRefreshSubject(), 'next');

    service.callRefreshPageSubject(mockParameter, DeviceConnectionHistoryListResource, false);

    expect(service.getDeviceConnectionHistoryListRefreshSubject().next).toHaveBeenCalledWith(mockParameter);
  });

  it('should emit detail refresh when resource is detail', () => {
    const mockParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    spyOn(service.getDeviceConnectionHistoryDetailRefreshSubject(), 'next');

    service.callRefreshPageSubject(mockParameter, DeviceConnectionHistoryDetailResource, false);

    expect(service.getDeviceConnectionHistoryDetailRefreshSubject().next).toHaveBeenCalledWith(mockParameter);
  });

  it('should load device connection history list successfully', async () => {
    const mockHttpResponse = new HttpResponse({
      status: 200,
      body: mockPagableResponse
    });

    deviceConnectionApiCallServiceSpy.getDeviceConnectionHistoryList.and.returnValue(of(mockHttpResponse));
    permissionServiceSpy.getConnectionHistoryPermission.and.returnValue(true);

    const pageObj = { page: 0, size: 10 };
    const result = await service.loadDeviceConnectionHistoryList(mockSearchRequestBody, pageObj);

    expect(permissionServiceSpy.getConnectionHistoryPermission).toHaveBeenCalledWith(PermissionAction.GET_CONNECTION_HISTORY_ACTION);
    expect(deviceConnectionApiCallServiceSpy.getDeviceConnectionHistoryList).toHaveBeenCalledWith(mockSearchRequestBody, pageObj);
    expect(result.success).toBe(true);
    expect(result.deviceConnectionHistoryList).toEqual([mockDeviceConnectionHistoryResponse]);
  });

  it('should handle insufficient permissions', async () => {
    permissionServiceSpy.getConnectionHistoryPermission.and.returnValue(false);

    const pageObj = { page: 0, size: 10 };
    const result = await service.loadDeviceConnectionHistoryList(mockSearchRequestBody, pageObj);

    expect(toastrServiceSpy.error).toHaveBeenCalledWith('Insufficient permissions to load device connection history list');
    expect(result.success).toBe(false);
  });

  it('should handle API errors', async () => {
    const mockError = new HttpErrorResponse({
      error: 'Server error',
      status: 500,
      statusText: 'Internal Server Error'
    });

    deviceConnectionApiCallServiceSpy.getDeviceConnectionHistoryList.and.returnValue(throwError(() => mockError));
    permissionServiceSpy.getConnectionHistoryPermission.and.returnValue(true);

    const pageObj = { page: 0, size: 10 };
    const result = await service.loadDeviceConnectionHistoryList(mockSearchRequestBody, pageObj);

    expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalledWith(mockError);
    expect(result.success).toBe(false);
  });

  it('should return empty result when API returns non-200 or null body', async () => {
    const mockHttpResponse = new HttpResponse({ status: 204, body: null });
    deviceConnectionApiCallServiceSpy.getDeviceConnectionHistoryList.and.returnValue(of(mockHttpResponse));
    permissionServiceSpy.getConnectionHistoryPermission.and.returnValue(true);

    const result = await service.loadDeviceConnectionHistoryList(mockSearchRequestBody, { page: 0, size: 10 });
    expect(result.success).toBe(false);
    expect(result.deviceConnectionHistoryList).toEqual([]);
  });

  it('should process filter search successfully', () => {
    const formValue = { serialNumber: 'TEST123' };

    spyOn(service, 'validateDeviceConnectionHistoryFilterForm').and.returnValue(true);
    spyOn(service, 'buildDeviceConnectionHistoryFilterRequestBody').and.returnValue(mockSearchRequestBody);
    spyOn(service, 'callDeviceConnectionHistoryListFilterRequestParameterSubject');

    const result = service.processFilterSearch(formValue, false, new ListingPageReloadSubjectParameter(true, true, false, false));

    expect(service.validateDeviceConnectionHistoryFilterForm).toHaveBeenCalledWith(formValue);
    expect(result).toBe(true);
  });

  it('should not process filter search when form is invalid', () => {
    const formValue = {};

    const result = service.processFilterSearch(formValue, true, new ListingPageReloadSubjectParameter(true, true, false, false));

    expect(result).toBe(false);
  });

  it('should return false when validation fails in processFilterSearch', () => {
    const formValue = {};
    spyOn(service, 'validateDeviceConnectionHistoryFilterForm').and.returnValue(false);
    const result = service.processFilterSearch(formValue, false, new ListingPageReloadSubjectParameter(true, true, false, false));
    expect(result).toBe(false);
  });

  it('should validate form with at least one filter', () => {
    const formValue = { serialNumber: 'TEST123' };

    const result = service.validateDeviceConnectionHistoryFilterForm(formValue);

    expect(result).toBe(true);
  });

  it('should not validate form with no filters', () => {
    const formValue = { serialNumber: null, deviceModel: null, manufacturer: null, osType: [], lastConnectedDateAndTime: null };

    const result = service.validateDeviceConnectionHistoryFilterForm(formValue);

    expect(result).toBe(false);
    expect(toastrServiceSpy.info).toHaveBeenCalledWith(COMMON_SELECT_FILTER);
  });

  it('should build device connection history filter request body', () => {
    const formValue = { serialNumber: 'TEST123', deviceModel: 'TestModel', manufacturer: 'TestManufacturer', osType: [], lastConnectedDateAndTime: 1672531200000 };

    const result = service.buildDeviceConnectionHistoryFilterRequestBody(formValue);

    expect(result.manufacturer).toBe('TestManufacturer');
    expect(result.deviceModel).toBe('TestModel');
    expect(result.deviceSerialNumber).toBe('TEST123');
  });

  it('should build request with osType when present', () => {
    const formValue = { serialNumber: 'TEST123', deviceModel: 'TestModel', manufacturer: 'TestManufacturer', osType: ['WINDOWS'], lastConnectedDateAndTime: 1672531200000 };

    const result = service.buildDeviceConnectionHistoryFilterRequestBody(formValue);
    expect(result.osType).toBeUndefined();
  });

  it('should clear all filters and refresh', () => {
    const mockParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
    spyOn(service, 'callDeviceConnectionHistoryListFilterRequestParameterSubject');

    service.clearAllFiltersAndRefresh(mockParameter);

    expect(service.callDeviceConnectionHistoryListFilterRequestParameterSubject).toHaveBeenCalled();
  });

  it('should set and get last applied device connection history search request', () => {
    service.setLastAppliedDeviceConnectionHistorySearchRequest(mockSearchRequestBody);

    const result = service.getLastAppliedDeviceConnectionHistorySearchRequest();

    expect(result).toEqual(mockSearchRequestBody);
  });

  it('should return null when no cached search request', () => {
    service.setLastAppliedDeviceConnectionHistorySearchRequest(null);

    const result = service.getLastAppliedDeviceConnectionHistorySearchRequest();

    expect(result).toBeNull();
  });

  it('should call filter page subject for reload page', () => {
    spyOn(service, 'callRefreshPageSubject');

    service.filterPageSubjectCallForReloadPage(true, false);

    expect(service.callRefreshPageSubject).toHaveBeenCalled();
  });
});
