<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading gif end -->
<!-- loading end -->


<body *ngIf="probeConnectionHistoryListDisplay">
    <!-- row start -->
    <div class="row">

        <!--############################################################-->
        <!--Filter start-->
        <!--############################################################-->
        <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)" id="probeConnectionHistoryFilterBtn">
            <label class="col-md-12 h5-tag">Filter</label>
            <div class="card mt-3">
                <div class="card-body">
                    <app-probe-connection-history-filter
                        [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
                        [probeConnectionHistorySearchRequestBody]="ProbeConnectionHistorySearchRequestBody"
                        [listPageRefreshForbackToDetailPage]="listPageRefreshForbackToDetailPage"></app-probe-connection-history-filter>
                </div>
            </div>
        </div>
        <!--############################################################-->
        <!--Filter End-->
        <!--############################################################-->

        <!--table Block Start-->
        <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
            <div class="container-fluid">
                <!--############################################################-->
                <!--############################################################-->
                <div class="row" class="headerAlignment">
                    <!--############################################################-->
                    <!--Left Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <!----------------------------------------------->
                        <!------------Show/hide filter-------------------->
                        <!----------------------------------------------->
                        <div class="dropdown" id="hideShowFilter">
                            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                                id="probeConnectionHistoryListHideShowButton">
                                <i class="fas fa-filter" aria-hidden="true"></i>
                                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
                            </button>
                        </div>
                        <!----------------------------------------------->
                        <!------------Pagnatation drp-------------------->
                        <!----------------------------------------------->
                        <div>
                            <label class="mb-0">Show entry</label>
                            <select [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                                (change)="changeDataSize($event)" id="probeConnectionHistoryListShowEntry">
                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                    <option [value]="dataSize">{{ dataSize }}</option>
                                </ng-template>
                            </select>
                        </div>
                    </div>
                    <!--############################################################-->
                    <!--Right Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <div class="btn-group btn-group-sm mr-2" role="group" style="display: inline-block;">
                            <button type="button" (click)="showDeviceConnectionListDisplay()"
                                class="btn btn-sm btn-cust-border">Device</button>
                            <button type="button" class="btn btn-sm btn-orange btn-cust-border"
                                id="probeToDevice">Probe</button>
                        </div>

                        <!------------------------------------------------>
                        <!----------------refresh------------------------->
                        <!------------------------------------------------>
                        <div>
                            <button class="btn btn-sm btn-orange" (click)="clickOnRefreshButton()"
                                id="refresh_probeConnectionHistoryList">
                                <em class="fa fa-refresh"></em>
                            </button>
                        </div>
                    </div>
                </div>
                <!--############################################################-->
                <!--############################################################-->
                <!-- selected Probe Connection start -->
                <div>Total {{totalRecord}} Probe Connection History(s)
                    <p
                        *ngIf="selectedProbeConnectionHistoryIdList != null && selectedProbeConnectionHistoryIdList.length > 0">
                        <strong>{{selectedProbeConnectionHistoryIdList.length}} Probe Connection History(s)
                            selected</strong>
                    </p>
                </div>
                <!-- selected probes end -->

                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- Probe Connection History table start --->
                <!-------------------------------------------->
                <!-------------------------------------------->
                <div class="commonTable">
                    <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
                        <!--###########################################-->
                        <!-- table header Start -->
                        <!--###########################################-->
                        <thead>
                            <tr class="thead-light">
                                <th class="checkox-table width-unset" *ngIf="showCheckBox">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="chkselectall"
                                            [id]="selectAllCheckboxId"
                                            (change)="selectAllItem($any($event.target)?.checked)">
                                        <label class="custom-control-label" [for]="selectAllCheckboxId"></label>
                                    </div>
                                </th>
                                <th><span class="text_nowrap">{{serialNumberOrHwId}}</span></th>
                                <th><span class="text_nowrap">{{partNumber}}</span></th>
                                <th><span class="text_nowrap">{{probeType}}</span></th>
                                <th><span class="text_nowrap">{{lastConnectedDate}}</span></th>
                            </tr>
                        </thead>
                        <!--###########################################-->
                        <!-- table body start -->
                        <!--###########################################-->
                        <tbody>
                            <tr *ngFor="let probeConnectionHistoryObj of probeConnectionHistoryResponseList;">
                                <td class="width-unset" *ngIf="showCheckBox">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input"
                                            [id]="chkPreFix+probeConnectionHistoryObj.id+chkPreFix"
                                            [name]="checkboxListName"
                                            (change)="selectCheckbox(probeConnectionHistoryObj, $any($event.target).checked)"
                                            [checked]="selectedProbeConnectionHistoryIdList.includes(probeConnectionHistoryObj.id)">
                                        <label class="custom-control-label"
                                            [for]="chkPreFix+probeConnectionHistoryObj.id+chkPreFix"></label>
                                    </div>
                                </td>
                                <td (click)="showProbeConnectionHistoryDetail(probeConnectionHistoryObj?.id)"
                                    class="spanunderline">
                                    <span class="text_nowrap">{{probeConnectionHistoryObj?.probeSerialNumber}}</span>
                                </td>
                                <td>
                                    <span class="text_nowrap">{{probeConnectionHistoryObj?.probePartNumber}}</span>
                                </td>
                                <td><span class="text_nowrap">{{probeConnectionHistoryObj?.probeType}}</span></td>
                                <td><span class="text_nowrap">{{probeConnectionHistoryObj?.lastConnectedDate |
                                        date:'MMM d, y, h:mm:ss a'}}</span></td>
                            </tr>
                        </tbody>
                        <!--###########################################-->
                        <!-- table body end -->
                        <!--###########################################-->
                    </table>

                </div>
                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- Probe Connection History table End ----->
                <!-------------------------------------------->
                <!-------------------------------------------->

                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination Start-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <div>
                    <div>Showing {{totalRecordDisplay}} out of {{totalRecord}} Probe Connection History(s)</div>
                    <div class="float-right">
                        <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage"
                            id="probeConnectionHistoryList-pagination" [maxSize]="5" [rotate]="true"
                            [boundaryLinks]="true" (pageChange)="loadPage(page)">
                        </ngb-pagination>
                    </div>
                </div>
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination end-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
            </div>
        </div>
        <!--table Block End-->
    </div>
    <!-- row end -->
</body>

<div *ngIf="probeConnectionHistoryDetailDisplay">
    <app-probe-connection-history-detail (showProbeConnectionHistoryList)="showProbeConnectionHistoryList()"
        [probeConnectionHistoryId]="probeConnectionHistoryId"></app-probe-connection-history-detail>
</div>