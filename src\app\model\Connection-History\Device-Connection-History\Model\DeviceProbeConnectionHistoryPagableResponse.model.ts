import { Pageable } from "src/app/model/common/pageable.model";
import { PageResponse } from "src/app/model/common/PageResponse.model";
import { Sort } from "src/app/model/common/sort.model";
import { DeviceProbeConnectionHistory } from "./DeviceProbeConnectionHistory.model";

export class DeviceProbeConnectionHistoryPagableResponse extends PageResponse {
    content: Array<DeviceProbeConnectionHistory>;

    constructor(
        pageable: Pageable,
        totalPages: number,
        last: boolean,
        totalElements: number,
        numberOfElements: number,
        first: boolean,
        sort: Sort,
        size: number,
        number: number,
        empty: boolean,
        content: Array<DeviceProbeConnectionHistory>
    ) {
        super(pageable, totalPages, last, totalElements, numberOfElements, first, sort, size, number, empty);
        this.content = content;
    }
}
