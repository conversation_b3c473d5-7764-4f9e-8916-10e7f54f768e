import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { firstValueFrom, Subject } from 'rxjs';
import { COMMON_SELECT_FILTER, ProbeConnectionHistoryDetailResource, ProbeConnectionHistoryListResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ProbeConnectionHistoryListResult } from 'src/app/model/Connection-History/Probe-Connection-History/Interface/ProbeConnectionHistoryListResult.interface';
import { ProbeConnectionHistoryFilterAction } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistoryFilterAction.model';
import { ProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistorySearchRequestBody.model';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { ProbeConnectionHistoryApiCallService } from '../probe-connection-history-api-call/probe-connection-history-api-call.service';

@Injectable({
  providedIn: 'root'
})
export class ProbeConnectionHistoryOperationService {
  constructor(
    private probeConnectionApiCallService: ProbeConnectionHistoryApiCallService,
    private exceptionHandlingService: ExceptionHandlingService,
    private toastrService: ToastrService,
    private permissionService: PermissionService,
    private commonOperationService: CommonOperationsService,
    private commonsService: CommonsService
  ) { }

  //Refresh Probe Connection History List
  private probeConnectionHistoryListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Refresh Probe Connection History Detail page
  private probeConnectionHistoryDetailRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //probe Connection History list filter
  private probeConnectionHistoryListFilterRequestParameterSubject = new Subject<ProbeConnectionHistoryFilterAction>();

  private lastAppliedProbeConnectionHistorySearchRequest: ProbeConnectionHistorySearchRequestBody = null;

  /**
  * Probe Connection History List Page Refresh After some Action
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getProbeConnectionHistoryListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.probeConnectionHistoryListRefreshSubject;
  }

  /**
  * Probe Connection History Detail Page Refresh After some Action
  * isReloadData false means delete operation and move list page
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getProbeConnectionHistoryDetailRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.probeConnectionHistoryDetailRefreshSubject;
  }

  /**
  * Get Probe Connection History List Filter Request Parameter Subject
  * Used by listing component to subscribe to filter changes
  * <AUTHOR>
  * @returns Subject<ProbeConnectionHistoryFilterAction>
  */
  public getProbeConnectionHistoryListFilterRequestParameterSubject(): Subject<ProbeConnectionHistoryFilterAction> {
    return this.probeConnectionHistoryListFilterRequestParameterSubject;
  }

  /**
  * Call Probe Connection History List Filter Request Parameter Subject
  * Used by filter component to emit filter changes
  * <AUTHOR>
  * @param probeConnectionHistoryFilterAction - The filter action containing search parameters
  */
  public callProbeConnectionHistoryListFilterRequestParameterSubject(probeConnectionHistoryFilterAction: ProbeConnectionHistoryFilterAction): void {
    this.probeConnectionHistoryListFilterRequestParameterSubject.next(probeConnectionHistoryFilterAction);
  }

  /**
  * This function call the subject for reload the page data
  * Note : (ProbeConnectionHistoryListResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter
  * @param resourceName
  * @param isFilterHidden
  */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean): void {
    if (resourceName == ProbeConnectionHistoryListResource) {
      if (isFilterHidden) {
        let probeConnectionHistorySearchRequestBody = new ProbeConnectionHistorySearchRequestBody(null, null, null, null);

        // Use cached filter data if available and not clearing filters
        if (this.lastAppliedProbeConnectionHistorySearchRequest && !listingPageReloadSubjectParameter.isClearFilter) {
          probeConnectionHistorySearchRequestBody = this.lastAppliedProbeConnectionHistorySearchRequest;
        }

        let probeConnectionHistoryFilterAction = new ProbeConnectionHistoryFilterAction(listingPageReloadSubjectParameter, probeConnectionHistorySearchRequestBody);
        this.callProbeConnectionHistoryListFilterRequestParameterSubject(probeConnectionHistoryFilterAction);
      } else {
        // When filter is visible, call refresh subject to trigger filter component
        this.probeConnectionHistoryListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    } else if (resourceName == ProbeConnectionHistoryDetailResource) {
      this.probeConnectionHistoryDetailRefreshSubject.next(listingPageReloadSubjectParameter);
    }
  }

  /**
   * Call filter page subject for reload page
   * Used to maintain filter state when navigating between listing and detail pages
   * <AUTHOR>
   * @param isDefaultPageNumber - Whether to reset to default page number
   * @param isClearFilter - Whether to clear filters
   */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    this.callRefreshPageSubject(listingPageReloadSubjectParameter, ProbeConnectionHistoryListResource, false);
  }

  /**
  * Load Probe connection history list with search parameters and pagination
  * Handles API call, response processing, and error handling
  * <AUTHOR>
  * @param probeConnectionHistorySearchRequestBody - Search criteria for filtering
  * @param pageObj - Pagination parameters (page, size)
  * @returns Promise with Probe connection history list result
  */
  public async loadProbeConnectionHistoryList(probeConnectionHistorySearchRequestBody: ProbeConnectionHistorySearchRequestBody, pageObj: any): Promise<ProbeConnectionHistoryListResult> {
    try {
      if (this.permissionService.getConnectionHistoryPermission(PermissionAction.GET_CONNECTION_HISTORY_ACTION)) {
        const response = await firstValueFrom(this.probeConnectionApiCallService.getProbeConnectionHistoryList(probeConnectionHistorySearchRequestBody, pageObj));

        if (response.status === 200 && response.body) {
          const probeConnectionHistoryData = response.body;
          return {
            success: true,
            probeConnectionHistoryList: probeConnectionHistoryData.content,
            totalRecordDisplay: probeConnectionHistoryData.numberOfElements,
            totalRecord: probeConnectionHistoryData.totalElements,
            localProbeConnectionHistoryList: probeConnectionHistoryData.content,
            totalItems: probeConnectionHistoryData.totalElements,
            page: probeConnectionHistoryData.number + 1
          };
        } else {
          return this.getEmptyProbeConnectionHistoryListResult();
        }
      } else {
        this.toastrService.error('Insufficient permissions to load Probe connection history list');
        return this.getEmptyProbeConnectionHistoryListResult();
      }
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return this.getEmptyProbeConnectionHistoryListResult();
    }
  }

  /**
  * Get empty Probe connection history list result for error cases
  * <AUTHOR>
  * @returns ProbeConnectionHistoryListResult
  */
  private getEmptyProbeConnectionHistoryListResult(): ProbeConnectionHistoryListResult {
    return {
      success: false,
      probeConnectionHistoryList: [],
      totalRecordDisplay: 0,
      totalRecord: 0,
      localProbeConnectionHistoryList: [],
      totalItems: 0,
      page: 0
    };
  }

  /**
  * Process filter search and validate form
  * <AUTHOR>
  * @param formValue - Form values from filter component
  * @param isFormInvalid - Whether form is invalid
  * @param defaultListingPageReloadSubjectParameter - Default reload parameters
  * @returns boolean indicating if search should proceed
  */
  public processFilterSearch(formValue: any, isFormInvalid: boolean, defaultListingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): boolean {
    if (isFormInvalid) {
      return false;
    }

    if (!this.validateProbeConnectionHistoryFilterForm(formValue)) {
      return false;
    }

    const probeConnectionHistorySearchRequestBody = this.buildProbeConnectionHistoryFilterRequestBody(formValue);
    const probeConnectionHistoryFilterAction = new ProbeConnectionHistoryFilterAction(defaultListingPageReloadSubjectParameter, probeConnectionHistorySearchRequestBody);
    this.callProbeConnectionHistoryListFilterRequestParameterSubject(probeConnectionHistoryFilterAction);
    return true;
  }

  /**
  * Validate Probe connection history filter form
  * <AUTHOR>
  * @param formValue - Form values to validate
  * @returns boolean indicating if validation passed
  */
  public validateProbeConnectionHistoryFilterForm(formValue: any): boolean {
    const {
      serialNumber, probeType, partNumber, fromLastConnectedDate, toLastConnectedDate
    } = formValue;

    const hasAnyFilter = serialNumber || partNumber ||
      probeType || fromLastConnectedDate || toLastConnectedDate;

    if (!hasAnyFilter) {
      this.toastrService.info(COMMON_SELECT_FILTER);
      return false;
    }

    if (!this.commonOperationService.dateValidation(fromLastConnectedDate, toLastConnectedDate)) {
      return false;
    }

    return true;
  }

  /**
  * Build Probe connection history filter request body from form values
  * <AUTHOR>
  * @param formValue - Form values from filter component
  * @returns ProbeConnectionHistorySearchRequestBody
  */
  public buildProbeConnectionHistoryFilterRequestBody(formValue: any): ProbeConnectionHistorySearchRequestBody {
    const lastConntectedFrom = this.commonsService.checkValueIsNullOrEmpty(formValue.fromLastConnectedDate) ? null : new Date(formValue.fromLastConnectedDate).getTime();
    const lastConntectedTo = this.commonsService.checkValueIsNullOrEmpty(formValue.toLastConnectedDate) ? null : this.commonsService.getEndTimeOfDay(new Date(formValue.toLastConnectedDate)).getTime();
    const probeValue = formValue.probeType?.[0] ?? null;

    const key = Object.keys(ProbeTypeEnum)
      .find(k => ProbeTypeEnum[k as keyof typeof ProbeTypeEnum] === probeValue);

    return new ProbeConnectionHistorySearchRequestBody(
      this.commonOperationService.getFilterValue(formValue.partNumber, false),
      key as ProbeTypeEnum,
      this.commonOperationService.getFilterValue(formValue.serialNumber, false),
      lastConntectedFrom,
      lastConntectedTo
    );
  }

  /**
  * Clear all filters and refresh the listing
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter - Reload parameters
  */
  public clearAllFiltersAndRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    const emptyFilterRequestBody = new ProbeConnectionHistorySearchRequestBody(null, null, null, null);
    const probeConnectionHistoryFilterAction = new ProbeConnectionHistoryFilterAction(listingPageReloadSubjectParameter, emptyFilterRequestBody);
    this.callProbeConnectionHistoryListFilterRequestParameterSubject(probeConnectionHistoryFilterAction);
  }

  /**
  * Set last applied Probe connection history search request for caching
  * <AUTHOR>
  * @param probeConnectionHistorySearchRequestBody - Filter data to cache
  */
  public setLastAppliedProbeConnectionHistorySearchRequest(probeConnectionHistorySearchRequestBody: ProbeConnectionHistorySearchRequestBody): void {
    this.lastAppliedProbeConnectionHistorySearchRequest = probeConnectionHistorySearchRequestBody;
  }

  /**
  * Get last applied Probe connection history search request from cache
  * <AUTHOR>
  * @returns ProbeConnectionHistorySearchRequestBody - Cached filter data
  */
  public getLastAppliedProbeConnectionHistorySearchRequest(): ProbeConnectionHistorySearchRequestBody {
    return this.lastAppliedProbeConnectionHistorySearchRequest;
  }
}
