import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { Subscription } from 'rxjs';
import { ITEMS_PER_PAGE, PROBE_CONNECTION_HISTORY_LAST_CONNECTED_FROM, PROBE_CONNECTION_HISTORY_PART_NUMBER, PROBE_CONNECTION_HISTORY_PROBE_TYPE, PROBE_CONNECTION_HISTORY_SERIAL_NUMBER_OR_HW_ID, ProbeConnectionHistoryListResource } from 'src/app/app.constants';
import { ProbeConnectionHistoryResponse } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistoryResponse.model';
import { ProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistorySearchRequestBody.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { ProbeConnectionHistoryOperationService } from '../probe-connection-history-services/probe-connection-history-operation/probe-connection-history-operation.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { isUndefined } from 'is-what';
import { ProbeConnectionHistoryFilterAction } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistoryFilterAction.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';

@Component({
  selector: 'app-probe-connection-history-list',
  templateUrl: './probe-connection-history-list.component.html',
  styleUrl: './probe-connection-history-list.component.css'
})
export class ProbeConnectionHistoryListComponent implements OnInit {

  @Output('showDeviceConnectionHistoryListDisplay') showDeviceConnectionHistoryListDisplay = new EventEmitter();

  loading: boolean = false;

  //Page
  itemsPerPage: number = 0;
  page: number = 0;
  previousPage: number = 0;
  totalItems: number = 0;

  //Totel Count Display
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;

  //Page Size DropDown
  dataSizes: string[] = [];
  drpselectsize: number = ITEMS_PER_PAGE;

  //Filter
  isFilterComponentInitWithApicall: boolean = true;
  listPageRefreshForbackToDetailPage: boolean = false;
  isFilterHidden: boolean = true;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  //Probe connection Hostory List
  probeConnectionHistoryResponseList: ProbeConnectionHistoryResponse[] = [];

  //Permission
  connectioHistoryAdminPermission: boolean = false;

  //subscription
  subscriptionForProbeConnectionHistoryListFilterRequestParameter: Subscription;

  //Hide Show List and Detail Page
  probeConnectionHistoryDetailDisplay: boolean = false;
  probeConnectionHistoryListDisplay: boolean = false;
  probeConnectionHistoryId: number = null;

  //unique CheckBox Name
  chkPreFix = "peobeConnectionHistory";
  selectAllCheckboxId = "selectAllProbeConnection";
  checkboxListName = "probeConnectionItem[]";

  //selected Probe Connection Id Collect
  selectedProbeConnectionHistoryIdList: number[] = [];
  localProbeConnectionHistoryIdListArray: number[] = [];

  //Probe Connection History serach request body store
  ProbeConnectionHistorySearchRequestBody: ProbeConnectionHistorySearchRequestBody = null;

  //checkboxHide
  showCheckBox: boolean = false;

  //Constance
  serialNumberOrHwId: string = PROBE_CONNECTION_HISTORY_SERIAL_NUMBER_OR_HW_ID;
  partNumber: string = PROBE_CONNECTION_HISTORY_PART_NUMBER;
  probeType: string = PROBE_CONNECTION_HISTORY_PROBE_TYPE;
  lastConnectedDate: string = PROBE_CONNECTION_HISTORY_LAST_CONNECTED_FROM;

  constructor(
    private authservice: AuthJwtService,
    private commonsService: CommonsService,
    private commonCheckboxService: CommonCheckboxService,
    private permissionService: PermissionService,
    private probeConnectionHistoryOperationService: ProbeConnectionHistoryOperationService
  ) { }

  /**
  * <AUTHOR>
  */
  public ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.page = 0;
      this.dataSizes = this.commonsService.accessDataSizes();
      this.selectedProbeConnectionHistoryIdList = [];
      this.isFilterComponentInitWithApicall = true;
      this.listPageRefreshForbackToDetailPage = false;
      this.isFilterHidden = false;
      this.probeConnectionHistoryListDisplay = true;
      this.probeConnectionHistoryDetailDisplay = false;
      this.setConnectionHistoryPermission();
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.previousPage = 1;
    }
    this.subjectInit();
  }

  /**
  * Permission set
  */
  private setConnectionHistoryPermission(): void {
    this.connectioHistoryAdminPermission = this.permissionService.getConnectionHistoryPermission(PermissionAction.GET_CONNECTION_HISTORY_ACTION);
  }

  private subjectInit(): void {
    /**
    * This Subject call from Filter component
    * Load all the Data
    * <AUTHOR>
    */
    this.subscriptionForProbeConnectionHistoryListFilterRequestParameter = this.probeConnectionHistoryOperationService.getProbeConnectionHistoryListFilterRequestParameterSubject()?.subscribe((probeConnectionHistoryRequestParameter: ProbeConnectionHistoryFilterAction) => {
      if (probeConnectionHistoryRequestParameter.listingPageReloadSubjectParameter.isReloadData) {
        if (probeConnectionHistoryRequestParameter.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.selectedProbeConnectionHistoryIdList = [];
          this.resetPage()
        }
        this.loadAll(probeConnectionHistoryRequestParameter.probeConnectionHistoryBody);
      }
    });
  }

  /**
  * Destroy subscription
  * <AUTHOR>
  */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForProbeConnectionHistoryListFilterRequestParameter)) { this.subscriptionForProbeConnectionHistoryListFilterRequestParameter.unsubscribe() }
  }

  /**
  * Reset Page
  * <AUTHOR>
  */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
  * Clear all filter ,Reset Page and Reload the page
  * <AUTHOR>
  */
  public async refreshFilter(isClearFilter: boolean): Promise<void> {
    this.loading = true;
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, isClearFilter);
  }

  /**
  * Item par page Value Changes like (10,50,100)
  * <AUTHOR>
  * @param datasize
  */
  public changeDataSize(datasize: any): void {
    this.setLoadingStatus(true);
    this.selectedProbeConnectionHistoryIdList = [];
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
  * single Checkbox Select
  * <AUTHOR>
  * @param probeConnectionHistoryObj 
  * @param isChecked 
  */
  public selectCheckbox(probeConnectionHistoryObj: ProbeConnectionHistoryResponse, isChecked: boolean): void {
    if (isChecked) {
      this.selectedProbeConnectionHistoryIdList.push(probeConnectionHistoryObj.id);
    } else {
      let index = this.selectedProbeConnectionHistoryIdList.findIndex(obj => obj == probeConnectionHistoryObj.id);
      this.selectedProbeConnectionHistoryIdList.splice(index, 1);
    }
    this.defaultSelectAll();
  }

  /**
  * select All checkbox select or deSelect
  * <AUTHOR>
  */
  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localProbeConnectionHistoryIdListArray, this.selectedProbeConnectionHistoryIdList, this.selectAllCheckboxId);
  }

  /**
  * Select All CheckBox
  * <AUTHOR>
  * @param isChecked 
  */
  public selectAllItem(isChecked: boolean): void {
    this.selectedProbeConnectionHistoryIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localProbeConnectionHistoryIdListArray, this.selectedProbeConnectionHistoryIdList, this.checkboxListName);
  }

  /**
  * Change The Page
  * callProbeConnectionHistoryListRefreshSubject ->Call the filter component
  * filter not clear and send with filter requrest and load data
  * <AUTHOR>
  * @param page
  */
  public loadPage(page: any): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.commonCheckboxService.clearSelectAllCheckbox(this.selectAllCheckboxId);
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }

  /**
  * Call Filter component subject and reload page
  * <AUTHOR>
  * @param isDefaultPageNumber 
  * @param isClearFilter 
  */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false)
    this.probeConnectionHistoryOperationService.callRefreshPageSubject(listingPageReloadSubjectParameter, ProbeConnectionHistoryListResource, this.isFilterHidden);
  }

  /**
  * Toggle Filter
  * <AUTHOR>
  * @param id 
  */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
  * Probe Connection History List API call using operation service
  * Store search request body for filter persistence
  * <AUTHOR>
  * @param probeConnectionHistorySearchRequestBody
  */
  public async loadAll(probeConnectionHistorySearchRequestBody: ProbeConnectionHistorySearchRequestBody): Promise<void> {
    this.setLoadingStatus(true);

    this.ProbeConnectionHistorySearchRequestBody = probeConnectionHistorySearchRequestBody;

    const pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage,
    };

    const result = await this.probeConnectionHistoryOperationService.loadProbeConnectionHistoryList(probeConnectionHistorySearchRequestBody, pageObj);

    if (result.success) {
      this.probeConnectionHistoryResponseList = result.probeConnectionHistoryList;
      this.totalRecordDisplay = result.totalRecordDisplay;
      this.totalRecord = result.totalRecord;
      this.totalItems = result.totalItems;
      this.page = result.page;
      this.setLocalProbeConnectionHistoryId(result.localProbeConnectionHistoryList);
    } else {
      this.probeConnectionHistoryResponseList = [];
      this.totalRecordDisplay = 0;
      this.totalRecord = 0;
      this.totalItems = 0;
    }

    this.setLoadingStatus(false);
    this.defaultSelectAll();
  }

  /**
  * Local Probe Connection History list create for Select all Checkbox
  * <AUTHOR>
  * @param probeConnectionHistoryIdList 
  */
  public setLocalProbeConnectionHistoryId(probeConnectionHistoryIdList: ProbeConnectionHistoryResponse[]): void {
    this.localProbeConnectionHistoryIdListArray = [];
    for (let salesOrderObj of probeConnectionHistoryIdList) {
      this.localProbeConnectionHistoryIdListArray.push(salesOrderObj.id);
    }
    this.defaultSelectAll();
  }

  /**
  * Loading Status 
  * <AUTHOR>
  */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
  }


  /**
  * Show Probe Connection History List 
  * 
  * <AUTHOR>
  */
  public showProbeConnectionHistoryList(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = true;
    this.probeConnectionHistoryId = null;
    this.probeConnectionHistoryListDisplay = true;
    this.probeConnectionHistoryDetailDisplay = false;
    this.selectedProbeConnectionHistoryIdList = [];
    if (this.isFilterHidden) {
      this.filterPageSubjectCallForReloadPage(true, false);
    }
  }

  /**
  * Show Probe Connection History Detail
  * 
  * @param id 
  * 
  * <AUTHOR>
  */
  public showProbeConnectionHistoryDetail(id: number): void {
    this.probeConnectionHistoryId = id;
    this.probeConnectionHistoryListDisplay = false;
    this.probeConnectionHistoryDetailDisplay = true;
  }

  /**
  * Show Device Connection History List Page
  * 
  * <AUTHOR>
  */
  public showDeviceConnectionListDisplay(): void {
    this.showDeviceConnectionHistoryListDisplay.emit();
  }

  /**
  * Refresh button click
  *
  * <AUTHOR>
  */
  public async clickOnRefreshButton(): Promise<void> {
    this.loading = true;
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, false);
  }

}
