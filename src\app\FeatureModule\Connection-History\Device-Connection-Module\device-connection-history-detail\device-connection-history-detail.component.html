<!-- loading start -->
<div class="ringLoading" *ngIf="loading">
    <!-- loading gif start -->
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
    <!-- loading gif end -->
</div>
<!-- loading end -->

<body class="bg-white" *ngIf="deviceConnectionHistoryDisplay" id="deviceConnectionHistoryDetail">

    <!-- main container start -->
    <div class="container-fluid">
        <!-- row start -->
        <div class="row">
            <div class="col-md-12">
                <!-- device detail header row start -->
                <div class="row headerAlignment">
                    <!-- device detail header start -->
                    <label class="childFlex h5-tag">Device Connection History Detail</label>
                    <!-- device detail header end -->

                    <div class="childFlex">

                        <button class="btn btn-sm btn-orange" (click)="navigateToDeviceDetail()">Device Detail</button>

                        <!-- Export Probe Connection History button start -->
                        <button class="btn btn-sm btn-outline-secondary device-back-btn ml-2"
                            (click)="exportConnectionHistory()">
                            <i class="fa fa-file-export" aria-hidden="true"></i>&nbsp;&nbsp;Export Connection History
                        </button>
                        <!-- Export Probe Connection History button end -->

                        <!-- back button start -->
                        <button class="btn btn-sm btn-outline-secondary device-back-btn ml-2" (click)="back()"><i
                                class="fa fa-reply" aria-hidden="true"></i>&nbsp;&nbsp;{{backBtnText}}</button>
                        <!-- back button end -->
                        <!-- refresh button start -->
                        <button class="btn btn-sm btn-orange ml-2" (click)="refreshDeviceDetailPage()"
                            id="refreshDeviceDetailPage"><em class="fa fa-refresh"></em></button>
                        <!-- refresh button end -->
                    </div>
                </div>
                <!-- Device Connection History header row end -->
                <!-- Device Connection History fields row start -->
                <div class="row">
                    <div class="col-md-12">
                        <!-- main card start -->
                        <div class="card">
                            <div class="card-body">
                                <div class="card shadow">
                                    <div class="card-body">

                                        <div class="row">
                                            <!-- deviceSerialNumber field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{serialNumberOrHwId}}</strong></label>
                                                    <!-- hardware field input start -->
                                                    <input type="text" class="form-control" name="deviceSerialNumber"
                                                        [value]="deviceConnectionHistoryDetailResponse?.deviceSerialNumber"
                                                        required readonly>
                                                    <!-- deviceSerialNumber field input end -->
                                                </div>
                                            </div>

                                            <!----------------------------->
                                            <!-- deviceModel  field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{deviceModel}}</strong></label>
                                                    <input type="text" class="form-control" name="deviceModel"
                                                        [value]="deviceConnectionHistoryDetailResponse?.deviceModel"
                                                        readonly>

                                                </div>
                                            </div>
                                            <!-- deviceModel  field end -->
                                            <!----------------------------->

                                            <!-- osType state field start -->
                                            <div class="col-md-3">
                                                <!-- osType state form group -->
                                                <div class="form-group">
                                                    <label><strong class="">{{osType}}</strong></label>
                                                    <!-- osType state inout start -->
                                                    <input type="text" class="form-control" name="osType"
                                                        [value]="osTypeEnum[deviceConnectionHistoryDetailResponse?.osType]"
                                                        readonly>
                                                    <!-- osType state inout end -->
                                                </div>
                                            </div>
                                            <!-- osType state field end -->

                                            <!-- manufacturer field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{manufacturer}}</strong></label>
                                                    <!-- manufacturer input start -->
                                                    <input type="text" class="form-control" name="manufacturer"
                                                        [value]="deviceConnectionHistoryDetailResponse?.manufacturer"
                                                        readonly>
                                                    <!-- manufacturer input end -->
                                                </div>
                                            </div>
                                            <!-- manufacturer field end -->

                                            <!-- lastConnectedDate field start -->
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label><strong class="">{{lastConnectedDate}}</strong></label>
                                                    <!-- lastConnectedDate input start -->
                                                    <input type="text" class="form-control" name="lastConnectedDate"
                                                        [value]="deviceConnectionHistoryDetailResponse?.lastConnectedDate| date:'MMM d, y, h:mm:ss a'"
                                                        readonly>
                                                    <!-- lastConnectedDate input end -->
                                                </div>
                                            </div>
                                            <!--form end-->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- main card end -->
                        </div>

                        <!-- Connection History Table start -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="card shadow">
                                            <div class="card-body">
                                                <div class="container">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <label class="mb-1 h5-tag"><span>Probe Connection
                                                                History</span></label>
                                                        <div class="d-flex justify-content-center align-items-center">
                                                            <select id="" [(ngModel)]="itemsPerPage"
                                                                class="btn btn-sm btn-outline-secondary device-back-btn mr-2"
                                                                (change)="changeDeviceHistoryDataSize($event)">
                                                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                                                    <option [value]="dataSize">{{ dataSize }}
                                                                    </option>
                                                                </ng-template>
                                                            </select>
                                                            <button class="btn btn-sm btn-orange mr-2"
                                                                (click)="toggleFilter()" id="toggleFilterButton">
                                                                <em class="fa fa-filter"></em>
                                                                {{hideShowFilterButtonText}}
                                                            </button>
                                                            <button class="btn btn-sm btn-orange"
                                                                (click)="refreshDeviceProbeConnectionHistory()"
                                                                id="refreshDeviceProbeConnectionHistory">
                                                                <em class="fa fa-refresh"></em>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <hr>

                                                    <!-- Filter and Table Row -->
                                                    <div class="row">
                                                        <!-- Filter Section (Left Side) -->
                                                        <div class="col-md-3" *ngIf="!isFilterHidden">
                                                            <div class="card">
                                                                <div class="card-body">
                                                                    <!-- <h6 class="card-title">Filter Options</h6> -->
                                                                    <form
                                                                        [formGroup]="filterProbeConnectionHistoryForm">

                                                                        <!-- Probe Serial Number Filter -->
                                                                        <div class="form-group">
                                                                            <label
                                                                                class="form-control-label"><strong>{{probeSerialNumber}}</strong></label>
                                                                            <input class="form-control form-control-sm"
                                                                                type="text"
                                                                                formControlName="probeSerialNumber" />
                                                                            <div
                                                                                *ngIf="(filterProbeConnectionHistoryForm.get('probeSerialNumber').touched || filterProbeConnectionHistoryForm.get('probeSerialNumber')?.dirty) &&
                                                                                filterProbeConnectionHistoryForm.get('probeSerialNumber').invalid">
                                                                                <div *ngIf="filterProbeConnectionHistoryForm.get('probeSerialNumber').errors['maxlength']"
                                                                                    class="pb-2">
                                                                                    <span
                                                                                        class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
                                                                                </div>
                                                                                <div *ngIf="filterProbeConnectionHistoryForm.get('probeSerialNumber').errors['pattern']"
                                                                                    class="pb-2">
                                                                                    <span
                                                                                        class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>

                                                                        <!-- Probe Type Filter -->
                                                                        <div class="form-group">
                                                                            <label
                                                                                class="form-control-label"><strong>{{probeType}}</strong></label>
                                                                            <ng-multiselect-dropdown
                                                                                formControlName="probeType"
                                                                                [settings]="dropdownSettingsForProbeType"
                                                                                [data]="probeTypesList">
                                                                            </ng-multiselect-dropdown>
                                                                        </div>

                                                                        <!-- Probe Part Number Filter -->
                                                                        <div class="form-group">
                                                                            <label
                                                                                class="form-control-label"><strong>{{probePartNumber}}</strong></label>
                                                                            <input class="form-control form-control-sm"
                                                                                type="text"
                                                                                formControlName="probePartNumber" />
                                                                            <div
                                                                                *ngIf="(filterProbeConnectionHistoryForm.get('probePartNumber').touched || filterProbeConnectionHistoryForm.get('probePartNumber')?.dirty) &&
                                                                                filterProbeConnectionHistoryForm.get('probePartNumber').invalid">
                                                                                <div *ngIf="filterProbeConnectionHistoryForm.get('probePartNumber').errors['maxlength']"
                                                                                    class="pb-2">
                                                                                    <span
                                                                                        class="alert-color font-12">{{textBoxMaxLengthMessage}}</span>
                                                                                </div>
                                                                                <div *ngIf="filterProbeConnectionHistoryForm.get('probePartNumber').errors['pattern']"
                                                                                    class="pb-2">
                                                                                    <span
                                                                                        class="alert-color font-12">{{specialCharacterErrorMessage}}</span>
                                                                                </div>
                                                                            </div>
                                                                        </div>

                                                                        <!-- Connection Type Filter -->
                                                                        <div class="form-group">
                                                                            <label
                                                                                class="form-control-label"><strong>{{connectionType}}</strong></label>
                                                                            <ng-multiselect-dropdown
                                                                                formControlName="connectionType"
                                                                                [settings]="dropdownSettingsForConnectionType"
                                                                                [data]="connectionTypesList">
                                                                            </ng-multiselect-dropdown>
                                                                        </div>

                                                                        <!-- Filter Action Buttons -->
                                                                        <hr class="mt-1 mb-2">
                                                                        <div class="d-flex">
                                                                            <button class="btn btn-sm btn-orange mr-2"
                                                                                (click)="searchProbeConnectionHistory()"
                                                                                [disabled]="filterProbeConnectionHistoryForm.invalid">
                                                                                {{searchBtnText}}
                                                                            </button>
                                                                            <button class="btn btn-sm btn-orange"
                                                                                (click)="clearFilter()">
                                                                                {{clearBtnText}}
                                                                            </button>
                                                                        </div>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Table Section (Right Side or Full Width) -->
                                                        <div [ngClass]="isFilterHidden ? 'col-md-12' : 'col-md-9'">

                                                            <!-- ------------------------------------------------------- -->
                                                            <!-- table start -->
                                                            <!-- ------------------------------------------------------- -->
                                                            <!-- Table wrapper for horizontal scroll on small devices -->
                                                            <div class="table-responsive">
                                                                <table class="table table-sm table-bordered">
                                                                    <thead>
                                                                        <tr class="thead-light">
                                                                            <th class="nowrap">{{probeSerialNumber}}
                                                                            </th>
                                                                            <th class="nowrap">{{probeType}}</th>
                                                                            <th class="nowrap">{{probePartNumber}}</th>
                                                                            <th class="nowrap">{{appVersion}}</th>
                                                                            <th class="nowrap">{{packageVersion}}</th>
                                                                            <th class="nowrap">{{pimsDbVersion}}</th>
                                                                            <th class="nowrap">{{settingDbVersion}}</th>
                                                                            <th class="nowrap">{{upsDbVersion}}</th>
                                                                            <th class="nowrap">{{timezone}}</th>
                                                                            <th class="nowrap">
                                                                                {{probeLastConnectedDate}}</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <ng-template ngFor let-probeHistory
                                                                            [ngForOf]="deviceProbeConnectionHistoryList">
                                                                            <tr>
                                                                                <td (click)="navigateToProbeDetail(probeHistory.id)"
                                                                                    class="spanunderline"
                                                                                    id="pcgListToDeatil">
                                                                                    {{probeHistory.probeSerialNumber}}
                                                                                </td>
                                                                                <td>{{probeTypeEnum[probeHistory.probeType]}}
                                                                                </td>
                                                                                <td>{{probeHistory.probePartNumber}}
                                                                                </td>
                                                                                <td>{{probeHistory.appVersion}}</td>
                                                                                <td>{{probeHistory.osVersion}}</td>
                                                                                <td>{{probeHistory.pimsDbVersion}}</td>
                                                                                <td>{{probeHistory.settingsDbVersion}}
                                                                                </td>
                                                                                <td>{{probeHistory.upsDbVersion}}</td>
                                                                                <td>{{probeHistory.timezone}}</td>
                                                                                <td>{{probeHistory.lastConnectedDate |
                                                                                    date:'MMM d,y, h:mm:ssa'}}</td>
                                                                            </tr>
                                                                        </ng-template>
                                                                    </tbody>
                                                                </table>
                                                            </div>

                                                            <!-- ------------------------------------------------------- -->
                                                            <!-- table end -->
                                                            <!-- ------------------------------------------------------- -->
                                                            <!--pagination Start-->
                                                            <div>
                                                                <div>Showing {{totalRecordDisplay}} out of
                                                                    {{totalRecord}}
                                                                    Probes</div>
                                                                <div class="float-right">
                                                                    <!-- ngb pagination start -->
                                                                    <ngb-pagination [collectionSize]="totalItems"
                                                                        [(page)]="page" [pageSize]="itemsPerPage"
                                                                        [maxSize]="5" [rotate]="true"
                                                                        [boundaryLinks]="true"
                                                                        (pageChange)="onPageChange($event)">
                                                                    </ngb-pagination>
                                                                    <!-- ngb pagination end -->
                                                                </div>
                                                            </div>
                                                            <!--pagination End-->
                                                        </div>
                                                        <!-- End Table Section -->
                                                    </div>
                                                    <!-- End Filter and Table Row -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Connection History Table end -->
                            </div>
                            <!-- device detail fields row end -->
                        </div>
                    </div>
                    <!-- row end -->
                </div>
                <!-- main container end -->
            </div>
        </div>
    </div>
</body>

<!----------------------------------------------------------->
<!-----------Probe Detail Page Start------------------------->
<!----------------------------------------------------------->
<ng-template [ngIf]="probeDetailPageDisplay">
    <app-ots-probes-detail [probeId]="productEntityId" [resource]="deviceConnectionHistoryDetailResource"
        (showOtsProbe)="showDeviceConnectionHistoryDetailPage()"></app-ots-probes-detail>
</ng-template>
<!----------------------------------------------------------->
<!-----------Probe Detail Page End------------------------->
<!----------------------------------------------------------->

<!------------------------------------------------------------->
<!------------Device Detail Page Start------------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="deviceDetailPageDisplay">
    <app-device-detail [deviceIdInput]="productEntityId" [resource]="deviceConnectionHistoryDetailResource"
        (showDevice)="showDeviceDetailPage()">
    </app-device-detail>
</ng-template>
<!------------------------------------------------------------>
<!------------Device Detail Page End--------------------------->
<!------------------------------------------------------------->