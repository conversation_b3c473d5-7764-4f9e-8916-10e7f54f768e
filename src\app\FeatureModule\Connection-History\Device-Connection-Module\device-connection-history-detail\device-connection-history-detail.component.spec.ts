import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { DeviceConnectionHistoryDetailComponent } from './device-connection-history-detail.component';
import { DeviceConnectionHistoryOperationService } from '../Device-Connection-History-Service/Device-Connection-History-Operation/device-connection-history-operation-service.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { DeviceConnectionHistoryDetails } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceConnectionHistoryDetails.model';
import { DeviceProbeConnectionHistory } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistory.model';
import { DeviceProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Device-Connection-History/Model/DeviceProbeConnectionHistorySearchRequestBody.model';
import { OSTypeEnum } from 'src/app/shared/enum/Probe/OSTypeEnum.enum';
import { ConnectionTypeEnum } from 'src/app/shared/enum/ConnectionHistory/ConnectionTypeEnum.enum';
import { DeviceConnectionHistoryDetailResource } from 'src/app/app.constants';

describe('DeviceConnectionHistoryDetailComponent', () => {
  let component: DeviceConnectionHistoryDetailComponent;
  let fixture: ComponentFixture<DeviceConnectionHistoryDetailComponent>;
  let deviceConnectionHistoryOperationServiceSpy: jasmine.SpyObj<DeviceConnectionHistoryOperationService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  let keyValueMappingServiceSpy: jasmine.SpyObj<KeyValueMappingServiceService>;
  let multiSelectDropDownSettingServiceSpy: jasmine.SpyObj<MultiSelectDropDownSettingService>;
  let validationServiceSpy: jasmine.SpyObj<ValidationService>;

  const mockDeviceConnectionHistoryDetails: DeviceConnectionHistoryDetails = {
    id: 1,
    deviceSerialNumber: 'TEST123',
    deviceModel: 'TestModel',
    manufacturer: 'TestManufacturer',
    osType: OSTypeEnum.BRIDGE,
    connectionType: ConnectionTypeEnum.INTERNAL,
    lastConnectedDate: 1672531200000
  };

  const mockDeviceProbeConnectionHistory: DeviceProbeConnectionHistory = new DeviceProbeConnectionHistory(
    1,
    'PROBE123',
    'TestProbeType',
    'PART123',
    ConnectionTypeEnum.INTERNAL,
    '1.0.0',
    '1.0.0',
    '1.0.0',
    '1.0.0',
    'UTC',
    '1.0.0',
    1672531200000
  );

  beforeEach(async () => {
    deviceConnectionHistoryOperationServiceSpy = jasmine.createSpyObj('DeviceConnectionHistoryOperationService', [
      'loadDeviceConnectionHistoryDetail',
      'loadDeviceProbeConnectionHistoryList',
      'exportDeviceProbeConnectionHistoryCSV'
    ]);
    commonsServiceSpy = jasmine.createSpyObj('CommonsService', ['accessDataSizes']);
    keyValueMappingServiceSpy = jasmine.createSpyObj('KeyValueMappingServiceService', ['getOSTypeList', 'getConnectionTypeList']);
    multiSelectDropDownSettingServiceSpy = jasmine.createSpyObj('MultiSelectDropDownSettingService', ['getMultiSelectDropDownSettings']);
    validationServiceSpy = jasmine.createSpyObj('ValidationService', ['validateForm']);

    commonsServiceSpy.accessDataSizes.and.returnValue(['10', '25', '50', '100']);
    keyValueMappingServiceSpy.getOSTypeList.and.returnValue([]);
    keyValueMappingServiceSpy.getConnectionTypeList.and.returnValue([]);
    multiSelectDropDownSettingServiceSpy.getMultiSelectDropDownSettings.and.returnValue({});

    await TestBed.configureTestingModule({
      declarations: [DeviceConnectionHistoryDetailComponent],
      imports: [ReactiveFormsModule],
      providers: [
        FormBuilder,
        { provide: DeviceConnectionHistoryOperationService, useValue: deviceConnectionHistoryOperationServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueMappingServiceSpy },
        { provide: MultiSelectDropDownSettingService, useValue: multiSelectDropDownSettingServiceSpy },
        { provide: ValidationService, useValue: validationServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DeviceConnectionHistoryDetailComponent);
    component = fixture.componentInstance;
    component.deviceConnectionHistoryId = 1;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should accept deviceConnectionHistoryId input', () => {
    const testId = 123;
    component.deviceConnectionHistoryId = testId;
    expect(component.deviceConnectionHistoryId).toBe(testId);
  });

  it('should handle null deviceConnectionHistoryId input', () => {
    component.deviceConnectionHistoryId = null;
    expect(component.deviceConnectionHistoryId).toBeNull();
  });

  it('should emit showDeviceConnectionHistoryList event', () => {
    spyOn(component.showDeviceConnectionHistoryList, 'emit');
    component.back();
    expect(component.showDeviceConnectionHistoryList.emit).toHaveBeenCalled();
  });

  it('should initialize component on ngOnInit', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryDetail.and.returnValue(
      Promise.resolve({ success: true, deviceDetail: mockDeviceConnectionHistoryDetails })
    );
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 1
      })
    );

    component.ngOnInit();
    tick();

    expect(component.loading).toBe(true);
    expect(component.deviceConnectionHistoryDisplay).toBe(true);
    expect(component.isFilterHidden).toBe(false);
    expect(commonsServiceSpy.accessDataSizes).toHaveBeenCalled();
  }));

  it('should refresh device detail page data', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryDetail.and.returnValue(
      Promise.resolve({ success: true, deviceDetail: mockDeviceConnectionHistoryDetails })
    );
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 1
      })
    );

    component.refreshDeviceDetailPage();
    tick();

    expect(deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryDetail).toHaveBeenCalled();
    expect(deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList).toHaveBeenCalled();
  }));

  it('should load device connection history detail successfully', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryDetail.and.returnValue(
      Promise.resolve({ success: true, deviceDetail: mockDeviceConnectionHistoryDetails })
    );
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 1
      })
    );

    component.loadDeviceConnectionHistoryDetail();
    tick();

    expect(component.loading).toBe(true);
    expect(component.deviceConnectionHistoryDetailResponse).toEqual(mockDeviceConnectionHistoryDetails);
    expect(deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList).toHaveBeenCalled();
  }));

  it('should handle failed device connection history detail load', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryDetail.and.returnValue(
      Promise.resolve({ success: false })
    );

    component.loadDeviceConnectionHistoryDetail();
    tick();

    expect(component.deviceConnectionHistoryDetailResponse).toBeNull();
  }));

  it('should load device probe connection history list successfully', fakeAsync(() => {
    const mockResult = {
      success: true,
      deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
      totalRecordDisplay: 1,
      totalRecord: 1,
      localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
      totalItems: 1,
      page: 1
    };

    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve(mockResult)
    );

    component.loadDeviceProbeConnectionHistoryList();
    tick();

    expect(component.loading).toBe(false);
    expect(component.deviceProbeConnectionHistoryList).toEqual([mockDeviceProbeConnectionHistory]);
    expect(component.totalItems).toBe(1);
    expect(component.totalRecord).toBe(1);
  }));

  it('should handle failed device probe connection history list load', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: false,
        deviceProbeConnectionHistoryList: [],
        totalRecordDisplay: 0,
        totalRecord: 0,
        localDeviceProbeConnectionHistoryList: [],
        totalItems: 0,
        page: 0
      })
    );

    component.loadDeviceProbeConnectionHistoryList();
    tick();

    expect(component.deviceProbeConnectionHistoryList).toEqual([]);
    expect(component.totalItems).toBe(0);
  }));

  it('should handle page change', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 2
      })
    );

    component.onPageChange(2);
    tick();

    expect(component.page).toBe(2);
    expect(deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList).toHaveBeenCalled();
  }));

  it('should handle items per page change', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 1
      })
    );

    const mockEvent = { target: { value: '25' } };
    component.changeDeviceHistoryDataSize(mockEvent);
    tick();

    expect(component.itemsPerPage).toBe(25);
    expect(deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList).toHaveBeenCalled();
  }));

  it('should toggle filter visibility', () => {
    component.isFilterHidden = true;
    component.toggleFilter();
    expect(component.isFilterHidden).toBe(false);

    component.toggleFilter();
    expect(component.isFilterHidden).toBe(true);
  });

  it('should search probe connection history', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 1
      })
    );

    component.searchProbeConnectionHistory();
    tick();

    expect(component.page).toBe(1);
    expect(deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList).toHaveBeenCalled();
  }));

  it('should clear filter', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 1
      })
    );

    component.clearFilter();
    tick();

    expect(component.page).toBe(1);
    expect(component.deviceProbeConnectionHistorySearchRequestBody).toEqual(jasmine.any(DeviceProbeConnectionHistorySearchRequestBody));
    expect(deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList).toHaveBeenCalled();
  }));

  it('should export connection history successfully', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.exportDeviceProbeConnectionHistoryCSV.and.returnValue(
      Promise.resolve()
    );

    component.exportConnectionHistory();
    tick();

    expect(deviceConnectionHistoryOperationServiceSpy.exportDeviceProbeConnectionHistoryCSV).toHaveBeenCalledWith(
      component.deviceConnectionHistoryId,
      component.deviceConnectionHistoryDetailResource
    );
    expect(component.loading).toBe(false);
  }));

  it('should handle export connection history error', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.exportDeviceProbeConnectionHistoryCSV.and.returnValue(
      Promise.reject(new Error('Export failed'))
    );

    component.exportConnectionHistory();
    tick();

    expect(component.loading).toBe(false);
  }));

  it('should navigate to probe detail', () => {
    component.navigateToProbeDetail(1);
    expect(component.productEntityId).toBe(1);
    expect(component.probeDetailPageDisplay).toBe(true);
    expect(component.deviceConnectionHistoryDisplay).toBe(false);
  });

  it('should navigate to device detail', () => {
    component.deviceConnectionHistoryDetailResponse = mockDeviceConnectionHistoryDetails;
    component.navigateToDeviceDetail();
    expect(component.productEntityId).toBe(mockDeviceConnectionHistoryDetails.id);
    expect(component.deviceDetailPageDisplay).toBe(true);
    expect(component.deviceConnectionHistoryDisplay).toBe(false);
  });

  it('should show device connection history detail page', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryDetail.and.returnValue(
      Promise.resolve({ success: true, deviceDetail: mockDeviceConnectionHistoryDetails })
    );
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 1
      })
    );

    component.showDeviceConnectionHistoryDetailPage();
    tick();

    expect(component.probeDetailPageDisplay).toBe(false);
    expect(component.deviceConnectionHistoryDisplay).toBe(true);
  }));

  it('should show device detail page', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceConnectionHistoryDetail.and.returnValue(
      Promise.resolve({ success: true, deviceDetail: mockDeviceConnectionHistoryDetails })
    );
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 1
      })
    );

    component.showDeviceDetailPage();
    tick();

    expect(component.deviceDetailPageDisplay).toBe(false);
    expect(component.deviceConnectionHistoryDisplay).toBe(true);
  }));

  it('should refresh device probe connection history', fakeAsync(() => {
    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 1
      })
    );

    component.refreshDeviceProbeConnectionHistory();
    tick();

    expect(deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList).toHaveBeenCalled();
  }));

  it('should handle search probe connection history with invalid form', () => {
    component.filterProbeConnectionHistoryForm = { invalid: true } as any;
    spyOn(component, 'loadDeviceProbeConnectionHistoryList');

    component.searchProbeConnectionHistory();

    expect(component.loadDeviceProbeConnectionHistoryList).not.toHaveBeenCalled();
  });

  it('should handle search probe connection history with valid form', fakeAsync(() => {
    component.filterProbeConnectionHistoryForm = {
      invalid: false,
      value: { probeSerialNumber: 'TEST123' }
    } as any;

    deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList.and.returnValue(
      Promise.resolve({
        success: true,
        deviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalRecordDisplay: 1,
        totalRecord: 1,
        localDeviceProbeConnectionHistoryList: [mockDeviceProbeConnectionHistory],
        totalItems: 1,
        page: 1
      })
    );

    component.searchProbeConnectionHistory();
    tick();

    expect(component.page).toBe(1);
    expect(deviceConnectionHistoryOperationServiceSpy.loadDeviceProbeConnectionHistoryList).toHaveBeenCalled();
  }));
});
