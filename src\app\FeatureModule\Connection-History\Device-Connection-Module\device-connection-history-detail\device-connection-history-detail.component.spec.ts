import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DeviceConnectionHistoryDetailComponent } from './device-connection-history-detail.component';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';

describe('DeviceConnectionHistoryDetailComponent', () => {
  let component: DeviceConnectionHistoryDetailComponent;
  let fixture: ComponentFixture<DeviceConnectionHistoryDetailComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DeviceConnectionHistoryDetailComponent],
      providers: [
        commonsProviders(null)
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DeviceConnectionHistoryDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should accept deviceConnectionHistoryId input', () => {
    const testId = 123;
    component.deviceConnectionHistoryId = testId;
    expect(component.deviceConnectionHistoryId).toBe(testId);
  });

  it('should handle null deviceConnectionHistoryId input', () => {
    component.deviceConnectionHistoryId = null;
    expect(component.deviceConnectionHistoryId).toBeNull();
  });

  it('should emit showDeviceConnectionHistoryList event', () => {
    spyOn(component.showDeviceConnectionHistoryList, 'emit');
    component.showDeviceConnectionHistoryList.emit();
    expect(component.showDeviceConnectionHistoryList.emit).toHaveBeenCalled();
  });
});
