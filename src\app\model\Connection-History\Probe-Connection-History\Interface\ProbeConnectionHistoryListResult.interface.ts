import { ProbeConnectionHistoryResponse } from "../Model/ProbeConnectionHistoryResponse.model";
/**
* Probe Connection History List Result interface for consistent response handling
*/
export interface ProbeConnectionHistoryListResult {
    success: boolean;
    probeConnectionHistoryList: ProbeConnectionHistoryResponse[];
    totalRecordDisplay: number;
    totalRecord: number;
    localProbeConnectionHistoryList: ProbeConnectionHistoryResponse[];
    totalItems: number;
    page: number;
}
