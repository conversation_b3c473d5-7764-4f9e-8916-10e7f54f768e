import { Component } from '@angular/core';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { PermissionService } from 'src/app/shared/permission.service';

@Component({
  selector: 'app-connection-history-module',
  templateUrl: './connection-history-module.component.html',
  styleUrl: './connection-history-module.component.css'
})
export class ConnectionHistoryModuleComponent {
  //permissions
  connectionHistoryDisplayPermissions: boolean = false;

  deviceConnectionHistoryListDisplay: boolean = false;
  probeConnectionHistoryListDisplay: boolean = false;

  constructor(
    private authservice: AuthJwtService,
    private permissionService: PermissionService) {
  }

  public ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.connectionHistoryDisplayPermissions = this.permissionService.getConnectionHistoryPermission(PermissionAction.GET_CONNECTION_HISTORY_ACTION);
      this.setDefalutPageDisplay();
    }
  }

  public setDefalutPageDisplay(): void {
    if (this.connectionHistoryDisplayPermissions) {
      this.showDeviceConnectionHistoryListDisplay();
    }
  }

  public showDeviceConnectionHistoryListDisplay(): void {
    this.hideShowListingPage(true, false);
  }
  public showProbeConnectionHistoryListDisplay(): void {
    this.hideShowListingPage(false, true);
  }
  private hideShowListingPage(deviceConnectionHistoryListDisplay: boolean, probeConnectionHistoryListDisplay: boolean): void {
    this.deviceConnectionHistoryListDisplay = deviceConnectionHistoryListDisplay;
    this.probeConnectionHistoryListDisplay = probeConnectionHistoryListDisplay;
  }
}
