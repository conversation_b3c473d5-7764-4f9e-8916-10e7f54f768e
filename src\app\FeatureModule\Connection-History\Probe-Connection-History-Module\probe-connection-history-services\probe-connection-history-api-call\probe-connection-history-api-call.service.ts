import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, Observable } from 'rxjs';
import { ProbeConnectionHistoryPegableResponse } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistoryPegableResponse.model';
import { ProbeConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/Probe-Connection-History/Model/ProbeConnectionHistorySearchRequestBody.model';
import { ConfigInjectService } from 'src/app/shared/InjectService/config-inject.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { createRequestOption } from 'src/app/shared/util/request-util';

@Injectable({
  providedIn: 'root'
})
export class ProbeConnectionHistoryApiCallService {
  public probeConnectionHistory = this.configInjectService.getServerApiUrl() + 'api/connection-history';

  constructor(
    private configInjectService: ConfigInjectService,
    private http: HttpClient,
    private commonsService: CommonsService,
  ) { }

  /**
  * Get Sales Order list
  * @param requestBody 
  * @param req 
  * @returns 
  */
  public getProbeConnectionHistoryList(requestBody: ProbeConnectionHistorySearchRequestBody, req: any): Observable<HttpResponse<ProbeConnectionHistoryPegableResponse>> {
    const options = createRequestOption(req);
    return this.http.post<ProbeConnectionHistoryPegableResponse>(this.probeConnectionHistory + "/probes/search", requestBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }
}
