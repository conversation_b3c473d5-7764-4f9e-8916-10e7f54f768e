import { ListingPageReloadSubjectParameter } from "../../../common/listingPageReloadSubjectParameter.model";
import { ProbeConnectionHistorySearchRequestBody } from "./ProbeConnectionHistorySearchRequestBody.model";

export class ProbeConnectionHistoryFilterAction {
    listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter;
    probeConnectionHistoryBody: ProbeConnectionHistorySearchRequestBody;

    constructor($listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, $probeConnectionHistorySearchRequestBody: ProbeConnectionHistorySearchRequestBody) {
        this.listingPageReloadSubjectParameter = $listingPageReloadSubjectParameter;
        this.probeConnectionHistoryBody = $probeConnectionHistorySearchRequestBody;
    }

}