import { ConnectionTypeEnum } from "src/app/shared/enum/ConnectionHistory/ConnectionTypeEnum.enum";

export class DeviceProbeConnectionHistorySearchRequestBody {
    probeSerialNumber?: string;
    probeType?: string;
    probePartNumber?: string;
    connectionType?: ConnectionTypeEnum;

    constructor(
        probeSerialNumber?: string,
        probeType?: string,
        probePartNumber?: string,
        connectionType?: ConnectionTypeEnum
    ) {
        this.probeSerialNumber = probeSerialNumber;
        this.probeType = probeType;
        this.probePartNumber = probePartNumber;
        this.connectionType = connectionType;
    }
}
